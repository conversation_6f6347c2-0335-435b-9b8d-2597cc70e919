# Partner & User Management System Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Database Schema](#database-schema)
3. [API Architecture](#api-architecture)
4. [Authentication & Security](#authentication--security)
5. [Partner Management](#partner-management)
6. [User Management](#user-management)
7. [API Endpoints](#api-endpoints)
8. [Error Handling](#error-handling)
9. [Performance Optimizations](#performance-optimizations)
10. [Testing Guidelines](#testing-guidelines)

## System Overview

The Partner & User Management System is a comprehensive PHP-based application built on the Phalcon framework. It provides secure, scalable management of partners and users with role-based access control, comprehensive logging, and efficient database operations.

### Key Features
- **Partner Management**: Complete CRUD operations for partners and their services
- **Partner Balance Management**: Real-time balance tracking and transaction history
- **Bet Slip Management**: Comprehensive partner bet slip tracking and reporting
- **User Management**: Comprehensive user lifecycle management with authentication
- **Role-Based Access Control**: Granular permissions system with partner linking
- **Dashboard Analytics**: Real-time charts and summary data for business insights
- **Services Management**: Partner service configuration and monitoring
- **Security**: Multi-layer authentication with token-based access and user-friendly error messages
- **Audit Trail**: Comprehensive logging and activity tracking
- **Performance**: Optimized database queries with caching support

### Technology Stack
- **Framework**: Phalcon PHP Framework
- **Database**: MySQL with optimized queries
- **Authentication**: JWT-like token system with Redis caching
- **Logging**: Multi-level logging (info, error, emergency)
- **Security**: Hash-based request validation

## Database Schema

### Core Tables Structure

#### Partners Table
```sql
CREATE TABLE partners (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    status ENUM('active','inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    address VARCHAR(255),
    country VARCHAR(100),
    msisdn VARCHAR(20)
);
```

#### Partner Balance Table
```sql
CREATE TABLE partner_balance (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    partner_id INT(11) NOT NULL UNIQUE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    bonus DECIMAL(15,2) DEFAULT 0.00,
    status TINYINT(1) DEFAULT 1,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id)
);
```

#### Partners Bet Slips Table
```sql
CREATE TABLE partners_bet_slips (
    slip_id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    partner_id INT(11) NOT NULL,
    bet_id BIGINT(20) NOT NULL,
    sport_id BIGINT(20),
    parent_match_id BIGINT(20) NOT NULL,
    parent_market_id BIGINT(20) NOT NULL,
    market_id BIGINT(20) NOT NULL,
    selection_id BIGINT(20) NOT NULL,
    outcome_name VARCHAR(250),
    odd_value DECIMAL(24,2) NOT NULL,
    pick VARCHAR(50),
    pick_name VARCHAR(250),
    winning_outcome VARCHAR(250),
    ht_scores VARCHAR(10),
    ft_scores VARCHAR(10),
    et_scores VARCHAR(10),
    extra_data TEXT,
    live_bet TINYINT(4) DEFAULT 0,
    status TINYINT(4) NOT NULL DEFAULT 0,
    resulting_type VARCHAR(255),
    start_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id)
);
```

#### Partners Bets Table
```sql
CREATE TABLE partners_bets (
    bet_id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    partner_id INT(11) NOT NULL,
    profile_id BIGINT(20) NOT NULL,
    bet_currency VARCHAR(4) NOT NULL DEFAULT 'KES',
    bet_amount DECIMAL(25,4) NOT NULL,
    bet_reference VARCHAR(45) NOT NULL,
    bet_transaction_id BIGINT(20) NOT NULL,
    bet_credit_transaction_id BIGINT(20),
    bet_type INT(2) NOT NULL DEFAULT 0,
    total_games INT(11) NOT NULL,
    live_events INT(3) NOT NULL DEFAULT 0,
    total_odd DECIMAL(25,4),
    possible_win DECIMAL(25,4) NOT NULL,
    witholding_tax DECIMAL(25,4) NOT NULL DEFAULT 0.0000,
    excise_tax DECIMAL(25,4) NOT NULL DEFAULT 0.0000,
    bet_attribution BIGINT(20) NOT NULL DEFAULT 1,
    browser_details VARCHAR(200),
    extra_data TEXT,
    created_by VARCHAR(70) NOT NULL,
    kra_report SMALLINT(6) NOT NULL DEFAULT 0,
    risk_state SMALLINT(6) NOT NULL DEFAULT 0,
    processed SMALLINT(6) NOT NULL DEFAULT 0,
    status SMALLINT(6) NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id)
);
```

#### Partner Services Table
```sql
CREATE TABLE partner_services (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    partner_id INT(11) NOT NULL,
    service_id INT(11) NOT NULL,
    rate_limit_per_minute INT(11) DEFAULT 60,
    status ENUM('active','inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id),
    FOREIGN KEY (service_id) REFERENCES services(id)
);
```

#### Partner Settings Table
```sql
CREATE TABLE partner_settings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    partner_id INT(11) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    callback_url VARCHAR(500),
    currency VARCHAR(3) DEFAULT 'USD',
    denomination VARCHAR(20) DEFAULT 'cents',
    timezone VARCHAR(50) DEFAULT 'UTC',
    billing_mode ENUM('prepay','postpay') DEFAULT 'prepay',
    rate_limit INT(11) DEFAULT 60,
    websites JSON,
    version INT(11) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id)
);
```

#### User Table
```sql
CREATE TABLE user (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    msisdn BIGINT(20) NOT NULL,
    user_name VARCHAR(65),
    display_name VARCHAR(200) NOT NULL,
    password VARCHAR(700) NOT NULL,
    status INT(11) NOT NULL DEFAULT 1,
    created_at DATETIME NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT(11) NOT NULL
);
```

#### User Login Table
```sql
CREATE TABLE user_login (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL UNIQUE,
    role_id INT(11),
    permission_acl VARCHAR(700),
    verification_code VARCHAR(250) NOT NULL,
    access_token VARCHAR(500) NOT NULL UNIQUE,
    success_attempts INT(11) DEFAULT 0,
    reset_attempts INT(11) DEFAULT 0,
    failed_reset_attempts INT(11) DEFAULT 0,
    failed_attempts INT(11) DEFAULT 0,
    cumlative_failed_attempts INT(11) DEFAULT 0,
    cumulative_success_login INT(11) DEFAULT 0,
    blocked_timeline DATETIME,
    reset_expiry_date DATETIME,
    last_logged_on DATETIME,
    activation_date DATETIME,
    last_reset_date DATETIME,
    access_token_expiry_date DATETIME,
    last_failed_attempt DATETIME,
    next_passwd_change_date DATETIME,
    status INT(11) DEFAULT 2,
    created_at DATETIME NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (role_id) REFERENCES user_roles(id)
);
```

#### User Roles Table
```sql
CREATE TABLE user_roles (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(30) NOT NULL UNIQUE,
    description VARCHAR(100) NOT NULL,
    permissions_acl VARCHAR(500),
    status INT(11) NOT NULL,
    created_by INT(3),
    updated_by INT(11) DEFAULT 0
);
```

#### User Permissions Table
```sql
CREATE TABLE user_permissions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(65) NOT NULL UNIQUE,
    description VARCHAR(150),
    status INT(3) DEFAULT 1,
    created_at DATETIME,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted DATETIME,
    user_id INT(11) NOT NULL,
    FOREIGN KEY (user_id) REFERENCES user(id)
);
```

#### User Blacklist Table
```sql
CREATE TABLE user_black_list (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL UNIQUE,
    blacklist_reason VARCHAR(200) NOT NULL,
    status INT(11) DEFAULT 1,
    unblacklisted_on DATETIME,
    blacklisted_on DATETIME,
    created_at DATETIME NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
);
```

#### User Logs Table
```sql
CREATE TABLE user_logs (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    activity VARCHAR(100) NOT NULL,
    request TEXT,
    created_at DATETIME NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
);
```

#### User Outbox Table
```sql
CREATE TABLE user_outbox (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(20) NOT NULL,
    sender_id VARCHAR(12) NOT NULL,
    message_type ENUM('LOGIN','REGISTER','RESET','BONUS','WINNING','NOTIFICATIONS','DEPOSIT','WITHDRAWAL','RECON') NOT NULL,
    message VARCHAR(700) NOT NULL,
    message_pages INT(11) DEFAULT 1,
    unique_id VARCHAR(40),
    status INT(1) DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
);
```

## API Architecture

### Request/Response Pattern
All API endpoints follow a consistent pattern:

#### Request Structure
```json
{
    "headers": {
        "x-authorization": "system_auth_key",
        "x-hash-key": "calculated_hash",
        "x-app-key": "application_key",
        "x-access": "user_access_token"
    },
    "body": {
        "timestamp": "unix_timestamp",
        "data_field_1": "value1",
        "data_field_2": "value2"
    }
}
```

#### Response Structure
```json
{
    "success": true/false,
    "code": 200,
    "message": "Operation successful",
    "data": {
        "result": [],
        "record_count": 0
    },
    "execution_time": "0.123 Sec"
}
```

### Controller Base Class
All controllers extend `ControllerBase` which provides:
- Database connection management
- Authentication utilities
- Logging mechanisms
- Response formatting
- Error handling
- Security validations

### Database Operations
The system uses optimized database operations:
- **rawSelect()**: For SELECT queries with parameters
- **rawSelectOneRecord()**: For single record retrieval
- **rawInsertBulk()**: For INSERT operations
- **rawUpdateWithParams()**: For UPDATE operations
- **tableQueryBuilder()**: For pagination and sorting

## Authentication & Security

### Multi-Layer Security
1. **Application Key Validation**: Validates the requesting application
2. **Authorization Key Check**: Verifies system-level authorization
3. **Hash Validation**: Ensures request integrity
4. **Access Token Verification**: Validates user session
5. **Permission Validation**: Checks user permissions for specific actions

### Enhanced Authentication Flow
The system now uses a centralized authentication flow through `ControllerHelpers::authenticateAndAuthorize()`:

```php
// Complete authentication and authorization in one call
$authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
if (!$authResult['success']) {
    $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
    return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
        'Request is not successful', $errorResponse['error'], true);
}

$userId = $authResult['user_id'];
```

### User-Friendly Error Messages
The system provides user-friendly error messages that don't expose technical details:

**Authentication Errors**:
- "Your session has expired or is invalid. Please log in again."
- "Authentication information is incomplete. Please check your request and try again."

**Authorization Errors**:
- "You do not have permission to perform this action. Contact your administrator if you believe this is an error."

**Processing Errors**:
- "We encountered an issue while processing your request. Please try again."
- "Your request is taking longer than expected. Please try again."

### Partner Count in Login Response
Login verification now includes partner information:

```json
{
    "code": 200,
    "message": "2FA verification successful. Access granted!",
    "data": {
        "token": "api_token",
        "uid": 123,
        "rid": 1,
        "expires": 360,
        "type": "minute",
        "permissions": [],
        "partner_count": 3,
        "partners": [
            {
                "id": 1,
                "name": "Partner A",
                "status": "active",
                "partner_role": "admin"
            }
        ]
    }
}
```

### Token Management
- **Access Tokens**: Encrypted JWT-like tokens stored in Redis
- **Token Expiry**: Configurable expiration times
- **Token Refresh**: Automatic token renewal on valid requests
- **Session Management**: Redis-based session storage

### Permission System
- **Role-Based Access**: Users assigned to roles with specific permissions
- **Granular Permissions**: Fine-grained control over system actions
- **Permission Inheritance**: Roles can inherit permissions
- **Dynamic Permissions**: Runtime permission validation

### Security Features
- **Request Hashing**: SHA-256 based request validation
- **Password Encryption**: Strong encryption for password storage
- **IP Validation**: Optional IP-based access control
- **Rate Limiting**: Configurable rate limits per partner/service
- **Audit Logging**: Comprehensive security event logging

## Partner Management

### Partner Entity Management
The system provides complete CRUD operations for partners:

#### Create Partner
- **Endpoint**: `POST /partner/create`
- **Validation**: Name uniqueness, status validation
- **Security**: Requires "Create Partners" permission
- **Features**: Automatic timestamp generation, status management

#### Read Partners
- **Endpoint**: `GET /partner/list`
- **Features**: Pagination, sorting, filtering, search
- **Performance**: Optimized queries with count optimization
- **Security**: Requires "View Partners" permission

#### Update Partner
- **Endpoint**: `PUT /partner/update`
- **Features**: Partial updates, conflict detection
- **Validation**: Existence check, name conflict resolution
- **Security**: Requires "Update Partners" permission

#### Delete Partner
- **Endpoint**: `DELETE /partner/delete`
- **Features**: Soft delete (status change), dependency checking
- **Safety**: Prevents deletion of partners with active services
- **Security**: Requires "Delete Partners" permission

### Partner Services Management
Manages the relationship between partners and available services:

#### Service Assignment
- **Rate Limiting**: Configurable per-minute limits (1-1000)
- **Status Management**: Active/inactive service control
- **Validation**: Partner and service existence verification
- **Conflict Prevention**: Duplicate service assignment prevention

#### Service Configuration
- **Dynamic Rate Limits**: Per-partner service rate limiting
- **Status Control**: Enable/disable services per partner
- **Audit Trail**: Complete change tracking
- **Bulk Operations**: Efficient batch processing

### Partner Settings Management
Comprehensive configuration management for partners:

#### API Configuration
- **API Key Management**: Secure key generation and storage
- **IP Whitelisting**: Optional IP-based access control
- **Callback URLs**: Webhook endpoint configuration
- **Version Control**: API version management

#### Business Configuration
- **Currency Settings**: Multi-currency support
- **Billing Modes**: Prepay/postpay configuration
- **Timezone Management**: Localized time handling
- **Website Integration**: Multiple domain support

## User Management

### User Lifecycle Management
Complete user management from creation to deletion:

#### User Creation
- **Validation**: Username/email uniqueness, password strength
- **Security**: Encrypted password storage, secure token generation
- **Initialization**: Default role assignment, permission setup
- **Notification**: Welcome messages, activation emails

#### User Authentication
- **Multi-Factor**: Optional 2FA with SMS/email verification
- **Session Management**: Secure token-based sessions
- **Failed Attempt Tracking**: Brute force protection
- **Account Lockout**: Temporary lockout after failed attempts

#### User Profile Management
- **Profile Updates**: Secure profile modification
- **Password Management**: Secure password reset flow
- **Status Management**: Account activation/deactivation
- **Role Management**: Dynamic role assignment

### User Security Features

#### Account Protection
- **Blacklist Management**: Temporary/permanent account restrictions
- **Activity Monitoring**: Comprehensive user activity logging
- **Suspicious Activity Detection**: Automated threat detection
- **Account Recovery**: Secure account recovery processes

#### Access Control
- **Role-Based Permissions**: Hierarchical permission system
- **Dynamic Permissions**: Runtime permission evaluation
- **Permission Inheritance**: Role-based permission inheritance
- **Audit Trail**: Complete permission change tracking

### User Communication

#### Message Management
- **Multi-Channel**: SMS, email, push notifications
- **Message Types**: Login, registration, reset, notifications
- **Queue Management**: Reliable message delivery
- **Status Tracking**: Delivery confirmation and retry logic

#### Notification System
- **Event-Driven**: Automatic notifications for key events
- **Template Management**: Customizable message templates
- **Delivery Optimization**: Intelligent delivery routing
- **Failure Handling**: Robust error handling and retry logic

## API Endpoints

### Partner Management Endpoints

#### GET /partners/v1/view
**Purpose**: Retrieve paginated list of partners

#### GET /partners/v1/partner_balance
**Purpose**: Retrieve partner balance information

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "partner_id": "optional|integer",
    "status": "optional|integer"
}
```

**Response**:
```json
{
    "code": 200,
    "message": "Queried X Partner Balance records successfully!",
    "data": {
        "record_count": 10,
        "result": [
            {
                "id": 1,
                "partner_id": 123,
                "partner_name": "Partner Name",
                "balance": "1500.00",
                "bonus": "250.00",
                "status": 1,
                "updated_at": "2025-01-13 10:30:00"
            }
        ]
    }
}
```

#### GET /partners/v1/partners_bet_slips
**Purpose**: Retrieve partner bet slips with filtering options

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "partner_id": "optional|integer",
    "bet_id": "optional|integer",
    "sport_id": "optional|integer",
    "status": "optional|integer",
    "live_bet": "optional|integer",
    "start_date": "optional|date",
    "end_date": "optional|date"
}
```

**Response**:
```json
{
    "code": 200,
    "message": "Queried X Partner Bet Slips successfully!",
    "data": {
        "record_count": 25,
        "result": [
            {
                "slip_id": 1001,
                "partner_id": 123,
                "partner_name": "Partner Name",
                "bet_id": 5001,
                "sport_id": 1,
                "market_id": 12345,
                "selection_id": 67890,
                "outcome_name": "Team A Win",
                "odd_value": "2.50",
                "pick": "1",
                "pick_name": "Home Win",
                "live_bet": 0,
                "status": 1,
                "start_time": "2025-01-13 15:00:00",
                "created_at": "2025-01-13 14:30:00"
            }
        ]
    }
}
```

#### GET /partner/list
**Purpose**: Retrieve paginated list of partners
**Method**: GET
**Authentication**: Required
**Permission**: "View Partners"

**Parameters**:
```json
{
    "page": 1,
    "limit": 50,
    "sort": "name|ASC",
    "status": "active",
    "start": "2024-01-01",
    "end": "2024-12-31",
    "skip_cache": 1
}
```

**Response**:
```json
{
    "success": true,
    "code": 200,
    "message": "Queried 25 Partners successfully!",
    "data": {
        "record_count": 25,
        "result": [
            {
                "id": 1,
                "name": "Partner A",
                "status": "active",
                "address": "123 Main St",
                "country": "USA",
                "msisdn": "+15550001",
                "created_at": "2024-01-15 10:30:00"
            }
        ]
    }
}
```

#### POST /partner/create
**Purpose**: Create new partner
**Method**: POST
**Authentication**: Required
**Permission**: "Create Partners"

**Request Body**:
```json
{
    "name": "New Partner",
    "status": "active",
    "address": "456 Business Ave",
    "country": "USA",
    "msisdn": "+***********"
}
```

**Response**:
```json
{
    "success": true,
    "code": 201,
    "message": "Partner created successfully!",
    "data": {
        "partner_id": 123
    }
}
```

#### PUT /partner/update
**Purpose**: Update existing partner
**Method**: PUT
**Authentication**: Required
**Permission**: "Update Partners"

**Request Body**:
```json
{
    "partner_id": 123,
    "name": "Updated Partner Name",
    "status": "inactive",
    "address": "789 New Address",
    "country": "Canada"
}
```

#### DELETE /partner/delete
**Purpose**: Soft delete partner (set status to inactive)
**Method**: DELETE
**Authentication**: Required
**Permission**: "Delete Partners"

**Request Body**:
```json
{
    "partner_id": 123
}
```

### Partner Services Endpoints

#### GET /partner/services
**Purpose**: Retrieve partner services with filtering
**Method**: GET
**Authentication**: Required
**Permission**: "View Partner Services"

**Parameters**:
```json
{
    "partner_id": 1,
    "service_id": 2,
    "status": "active",
    "page": 1,
    "limit": 50
}
```

**Response**:
```json
{
    "success": true,
    "code": 200,
    "message": "Queried 5 Partner Services successfully!",
    "data": {
        "record_count": 5,
        "result": [
            {
                "id": 13,
                "partner_id": 1,
                "service_id": 1,
                "rate_limit_per_minute": 60,
                "status": "active",
                "created_at": "2024-01-15 11:21:57",
                "partner_name": "Partner A",
                "service_name": "prematch"
            }
        ]
    }
}
```

#### POST /partner/service/create
**Purpose**: Assign service to partner
**Method**: POST
**Authentication**: Required
**Permission**: "Create Partner Services"

**Request Body**:
```json
{
    "partner_id": 1,
    "service_id": 3,
    "rate_limit_per_minute": 100,
    "status": "active"
}
```

### Bets Management Endpoints

#### GET /bets/v1/partner_balance
**Purpose**: Retrieve partner balance information from BetsController

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "partner_id": "optional|integer",
    "status": "optional|integer"
}
```

#### GET /bets/v1/partners_bet_slips
**Purpose**: Retrieve partner bet slips from BetsController

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "partner_id": "optional|integer",
    "bet_id": "optional|integer",
    "sport_id": "optional|integer",
    "status": "optional|integer",
    "live_bet": "optional|integer"
}
```

### Dashboard Endpoints

#### GET /dashboard/v1/charts
**Purpose**: Retrieve dashboard chart data for UI visualization

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "chart_type": "optional|string|values:all,partner_balance,bets_by_sport,daily_revenue,partner_performance",
    "period": "optional|string|values:1day,7days,30days,90days"
}
```

**Response**:
```json
{
    "code": 200,
    "message": "Dashboard charts data retrieved successfully!",
    "data": {
        "partner_balance": {
            "title": "Top 10 Partners by Balance",
            "type": "doughnut",
            "labels": ["Partner A", "Partner B"],
            "datasets": [{
                "data": [1500.00, 1200.00],
                "backgroundColor": ["#FF6384", "#36A2EB"]
            }]
        },
        "bets_by_sport": {
            "title": "Bets by Sport (7days)",
            "type": "bar",
            "labels": ["Football", "Basketball"],
            "datasets": [{
                "data": [150, 89],
                "backgroundColor": ["#FF6384", "#36A2EB"]
            }]
        }
    }
}
```

#### GET /dashboard/v1/summary
**Purpose**: Retrieve dashboard summary statistics

**Response**:
```json
{
    "code": 200,
    "message": "Dashboard summary retrieved successfully!",
    "data": {
        "total_partners": 25,
        "total_balance": "45000.00",
        "total_bonus": "5500.00",
        "total_bets": 1250,
        "total_stake": "125000.00",
        "total_possible_win": "275000.00",
        "active_services": 18,
        "recent_activity": []
    }
}
```

### Services Management Endpoints

#### GET /services/v1/view
**Purpose**: Retrieve services list with filtering

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "service_type": "optional|string",
    "status": "optional|integer",
    "partner_id": "optional|integer"
}
```

#### POST /services/v1/create
**Purpose**: Create a new service

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "service_name": "required|string",
    "service_type": "required|string",
    "partner_id": "required|integer",
    "endpoint_url": "optional|string",
    "api_key": "optional|string",
    "status": "optional|integer"
}
```

#### POST /services/v1/update/{serviceId}
**Purpose**: Update an existing service

### Transactions Endpoints

#### GET /transactions/v1/partner_balance_transactions
**Purpose**: Retrieve partner balance transaction history

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "partner_id": "optional|integer",
    "transaction_type": "optional|string|values:credit,debit",
    "status": "optional|integer",
    "start_date": "optional|date",
    "end_date": "optional|date"
}
```

#### GET /transactions/v1/partner_balance_summary
**Purpose**: Get partner balance summary statistics

**Request Parameters**:
```json
{
    "timestamp": "required|string",
    "partner_id": "optional|integer"
}
```

### User Management Endpoints

#### GET /user/list
**Purpose**: Retrieve paginated list of users
**Method**: GET
**Authentication**: Required
**Permission**: "View Users"

**Parameters**:
```json
{
    "page": 1,
    "limit": 50,
    "sort": "created_at|DESC",
    "status": 1,
    "role_id": 2,
    "search": "john",
    "start": "2024-01-01",
    "end": "2024-12-31"
}
```

**Response**:
```json
{
    "success": true,
    "code": 200,
    "message": "Queried 15 Users successfully!",
    "data": {
        "record_count": 15,
        "result": [
            {
                "id": 1,
                "msisdn": ************,
                "user_name": "john.doe",
                "display_name": "John Doe",
                "status": 1,
                "created_at": "2024-01-15 09:30:00",
                "role_id": 2,
                "role_name": "Administrator",
                "last_logged_on": "2024-01-20 14:30:00",
                "login_status": 1
            }
        ]
    }
}
```

#### POST /user/create
**Purpose**: Create new user account
**Method**: POST
**Authentication**: Required
**Permission**: "Create Users"

**Request Body**:
```json
{
    "msisdn": ************,
    "user_name": "jane.smith",
    "display_name": "Jane Smith",
    "password": "base64_encoded_password",
    "role_id": 3,
    "status": 1
}
```

#### POST /user/login
**Purpose**: Authenticate user and generate access token
**Method**: POST
**Authentication**: System level only

**Request Body**:
```json
{
    "username": "john.doe",
    "password": "base64_encoded_password",
    "dial_code": "+254"
}
```

**Response**:
```json
{
    "success": true,
    "code": 200,
    "message": "Login successful. Please verify with the OTP sent to your mobile number.",
    "data": {
        "uid": 1,
        "username": "john.doe",
        "requires_otp": true,
        "otp_expires_in": 3,
        "otp_sent": true
    }
}
```

#### POST /user/verify-otp
**Purpose**: Verify OTP and complete login
**Method**: POST
**Authentication**: System level only

**Request Body**:
```json
{
    "username": "john.doe",
    "verification_code": "12345",
    "dial_code": "+254"
}
```

**Response**:
```json
{
    "success": true,
    "code": 200,
    "message": "2FA verification successful. Access granted!",
    "data": {
        "token": "access_token_here",
        "uid": 1,
        "rid": 2,
        "expires": 60,
        "type": "minute",
        "permissions": [
            {"id": 1, "name": "View Users"},
            {"id": 2, "name": "Create Users"}
        ]
    }
}
```

## Error Handling

### Error Response Format
All errors follow a consistent format:

```json
{
    "success": false,
    "code": 422,
    "message": "Validation error description",
    "data": null,
    "execution_time": "0.045 Sec"
}
```

### Common Error Codes

#### Authentication Errors (401)
- **Invalid Key**: Application key validation failed
- **Invalid Token**: Access token expired or invalid
- **Authentication Failure**: User credentials invalid

#### Authorization Errors (403)
- **Insufficient Privileges**: User lacks required permissions
- **Account Blacklisted**: User account is blacklisted
- **Account Suspended**: User account is suspended

#### Validation Errors (422)
- **Missing Fields**: Required parameters not provided
- **Invalid Format**: Data format validation failed
- **Business Rule Violation**: Business logic constraints violated

#### Resource Errors (404)
- **Not Found**: Requested resource doesn't exist
- **No Results**: Query returned no results

#### Conflict Errors (409)
- **Duplicate Entry**: Resource already exists
- **Dependency Conflict**: Cannot delete due to dependencies

#### Server Errors (500)
- **Database Error**: Database operation failed
- **Internal Error**: Unexpected system error

### Error Logging
- **Multi-Level Logging**: Info, warning, error, emergency levels
- **Structured Logging**: JSON formatted log entries
- **Context Preservation**: Full request context in logs
- **Performance Tracking**: Execution time logging

## Performance Optimizations

### Database Optimizations

#### Query Optimization
- **Indexed Queries**: All foreign keys and search fields indexed
- **Prepared Statements**: All queries use parameter binding
- **Query Caching**: Configurable query result caching
- **Connection Pooling**: Efficient database connection management

#### Pagination Strategy
```php
// Efficient count query with main query
$sql = "SELECT (SELECT COUNT(p.id) FROM partners p $searchQuery) as trx_count,"
     . "p.id, p.name, p.status, p.created_at "
     . "FROM partners p $searchQuery $sorting";
```

#### Dynamic Query Building
```php
// Build queries dynamically to avoid unnecessary operations
$updateFields = [];
$updateParams = [':id' => $partnerId];

if ($name) {
    $updateFields[] = "name = :name";
    $updateParams[':name'] = $name;
}
if ($status) {
    $updateFields[] = "status = :status";
    $updateParams[':status'] = $status;
}

$updateSql = "UPDATE partners SET " . implode(', ', $updateFields) . " WHERE id = :id";
```

### Caching Strategy

#### Redis Integration
- **Session Storage**: User sessions stored in Redis
- **Token Caching**: Access tokens cached for fast validation
- **Query Caching**: Configurable query result caching
- **Rate Limiting**: Redis-based rate limiting implementation

#### Cache Invalidation
- **Smart Invalidation**: Targeted cache invalidation on updates
- **TTL Management**: Appropriate time-to-live settings
- **Cache Warming**: Proactive cache population for critical data

### Memory Management

#### Efficient Data Handling
- **Streaming Results**: Large result sets processed in chunks
- **Memory Cleanup**: Explicit memory cleanup in long-running processes
- **Object Pooling**: Reuse of expensive objects

#### Resource Management
- **Connection Cleanup**: Proper database connection cleanup
- **File Handle Management**: Efficient file resource management
- **Memory Monitoring**: Built-in memory usage tracking

## Testing Guidelines

### Unit Testing Strategy

#### Test Coverage Areas
1. **Authentication Methods**: Token validation, permission checking
2. **CRUD Operations**: Create, read, update, delete for all entities
3. **Validation Logic**: Input validation, business rule enforcement
4. **Error Handling**: Exception handling, error response formatting
5. **Security Features**: Hash validation, access control

#### Sample Test Cases

##### Partner Management Tests
```php
// Test partner creation with valid data
public function testCreatePartnerSuccess() {
    $data = [
        'name' => 'Test Partner',
        'status' => 'active',
        'country' => 'USA'
    ];
    $response = $this->callAPI('POST', '/partner/create', $data);
    $this->assertEquals(201, $response['code']);
    $this->assertArrayHasKey('partner_id', $response['data']);
}

// Test partner creation with duplicate name
public function testCreatePartnerDuplicateName() {
    $data = ['name' => 'Existing Partner'];
    $response = $this->callAPI('POST', '/partner/create', $data);
    $this->assertEquals(409, $response['code']);
    $this->assertContains('already exists', $response['message']);
}
```

##### User Authentication Tests
```php
// Test successful login
public function testUserLoginSuccess() {
    $data = [
        'username' => 'test.user',
        'password' => base64_encode('ValidPassword123!')
    ];
    $response = $this->callAPI('POST', '/user/login', $data);
    $this->assertEquals(200, $response['code']);
    $this->assertTrue($response['data']['requires_otp']);
}

// Test invalid credentials
public function testUserLoginInvalidCredentials() {
    $data = [
        'username' => 'test.user',
        'password' => base64_encode('WrongPassword')
    ];
    $response = $this->callAPI('POST', '/user/login', $data);
    $this->assertEquals(401, $response['code']);
}
```

### Integration Testing

#### API Integration Tests
- **End-to-End Workflows**: Complete user/partner lifecycle testing
- **Cross-Module Integration**: Testing interactions between modules
- **Database Integration**: Testing database operations and transactions
- **External Service Integration**: Testing SMS, email, and other external services

#### Performance Testing
- **Load Testing**: Testing system under normal load
- **Stress Testing**: Testing system limits and breaking points
- **Concurrency Testing**: Testing concurrent user operations
- **Database Performance**: Testing query performance under load

### Security Testing

#### Authentication Testing
- **Token Validation**: Testing token expiry, invalidation
- **Permission Testing**: Testing role-based access control
- **Brute Force Protection**: Testing account lockout mechanisms
- **Session Management**: Testing session security and cleanup

#### Input Validation Testing
- **SQL Injection**: Testing parameter binding effectiveness
- **XSS Prevention**: Testing input sanitization
- **Data Validation**: Testing business rule enforcement
- **Rate Limiting**: Testing API rate limiting functionality

## Deployment Guidelines

### Environment Configuration

#### Development Environment
```php
// config/config.php - Development settings
$config = [
    'database' => [
        'host' => 'localhost',
        'username' => 'dev_user',
        'password' => 'dev_password',
        'dbname' => 'partner_b2b_app_dev'
    ],
    'redis' => [
        'host' => 'localhost',
        'port' => 6379
    ],
    'logging' => [
        'level' => 'debug',
        'file' => '/var/log/app/development.log'
    ]
];
```

#### Production Environment
```php
// config/config.php - Production settings
$config = [
    'database' => [
        'host' => 'prod-db-cluster.example.com',
        'username' => 'prod_user',
        'password' => 'secure_prod_password',
        'dbname' => 'partner_b2b_app'
    ],
    'redis' => [
        'host' => 'redis-cluster.example.com',
        'port' => 6379
    ],
    'logging' => [
        'level' => 'error',
        'file' => '/var/log/app/production.log'
    ]
];
```

### Database Migration

#### Migration Scripts
```sql
-- Migration: Add partner settings table
CREATE TABLE partner_settings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    partner_id INT(11) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    -- ... other fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (partner_id) REFERENCES partners(id)
);

-- Add indexes for performance
CREATE INDEX idx_partner_settings_partner_id ON partner_settings(partner_id);
CREATE INDEX idx_partner_services_partner_service ON partner_services(partner_id, service_id);
CREATE INDEX idx_user_login_user_id ON user_login(user_id);
CREATE INDEX idx_user_logs_user_activity ON user_logs(user_id, activity);
```

#### Data Migration
```php
// Migration script for existing data
class PartnerDataMigration {
    public function migratePartnerSettings() {
        $partners = $this->db->query("SELECT id FROM partners WHERE status = 'active'");

        foreach ($partners as $partner) {
            $this->db->insert('partner_settings', [
                'partner_id' => $partner['id'],
                'api_key' => $this->generateApiKey(),
                'currency' => 'USD',
                'billing_mode' => 'prepay',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
}
```

### Monitoring and Maintenance

#### Health Checks
```php
// Health check endpoint
public function healthCheck() {
    $checks = [
        'database' => $this->checkDatabase(),
        'redis' => $this->checkRedis(),
        'disk_space' => $this->checkDiskSpace(),
        'memory' => $this->checkMemoryUsage()
    ];

    $overall = array_reduce($checks, function($carry, $check) {
        return $carry && $check['status'] === 'ok';
    }, true);

    return [
        'status' => $overall ? 'healthy' : 'unhealthy',
        'checks' => $checks,
        'timestamp' => date('c')
    ];
}
```

#### Performance Monitoring
- **Response Time Tracking**: Monitor API response times
- **Database Performance**: Track slow queries and connection usage
- **Memory Usage**: Monitor memory consumption patterns
- **Error Rate Monitoring**: Track error rates and patterns

#### Log Management
- **Log Rotation**: Implement log rotation to manage disk space
- **Log Aggregation**: Centralized logging for distributed deployments
- **Alert Configuration**: Set up alerts for critical errors
- **Performance Metrics**: Track key performance indicators

## Best Practices

### Code Quality
- **PSR Standards**: Follow PSR-1, PSR-2, PSR-4 coding standards
- **Documentation**: Comprehensive inline documentation
- **Type Hinting**: Use type hints for better code clarity
- **Error Handling**: Consistent exception handling patterns

### Security Best Practices
- **Input Validation**: Validate all input data
- **Output Encoding**: Encode output to prevent XSS
- **SQL Injection Prevention**: Use prepared statements exclusively
- **Authentication**: Implement strong authentication mechanisms
- **Authorization**: Enforce proper access controls
- **Audit Logging**: Log all security-relevant events

### Performance Best Practices
- **Database Optimization**: Use indexes, optimize queries
- **Caching Strategy**: Implement appropriate caching layers
- **Resource Management**: Proper cleanup of resources
- **Monitoring**: Continuous performance monitoring

### Maintenance Best Practices
- **Regular Updates**: Keep dependencies updated
- **Backup Strategy**: Regular database and file backups
- **Disaster Recovery**: Documented recovery procedures
- **Capacity Planning**: Monitor and plan for growth

## Conclusion

This Partner & User Management System provides a robust, secure, and scalable foundation for managing partners and users in a B2B environment. The system implements industry best practices for security, performance, and maintainability while providing comprehensive functionality for partner and user lifecycle management.

Key strengths of the system include:
- **Security**: Multi-layer authentication and authorization
- **Performance**: Optimized database operations and caching
- **Scalability**: Designed for horizontal and vertical scaling
- **Maintainability**: Clean code architecture and comprehensive documentation
- **Flexibility**: Configurable permissions and role-based access control

The system is production-ready and can be extended to meet additional business requirements while maintaining its core architectural principles.

