# MossBets B2B Back Office Application Documentation

## Overview
The MossBets B2B Back Office application is a comprehensive betting management system built with Phalcon PHP framework. It provides secure API endpoints for managing users, partners, bets, transactions, and generating reports with advanced dashboard analytics.

## System Architecture

### Framework & Technology Stack
- **Framework**: Phalcon PHP 4.x (Micro Application)
- **Database**: MySQL with read/write separation
- **Caching**: Redis for session management and data caching
- **Authentication**: JWT-based with multi-layer security
- **Architecture**: RESTful API with MicroCollection routing

### Security Features
- **Multi-layer Authentication**: JWT + HMAC signature validation
- **Permission-based Authorization**: Role-based access control (RBAC)
- **Request Integrity**: Timestamp validation and replay attack prevention
- **IP Whitelisting**: Partner-specific IP restrictions
- **Rate Limiting**: Endpoint-specific request throttling
- **Data Encryption**: Field-level encryption for sensitive data

## API Endpoints

### Authentication & User Management (`/system/`)

#### User Authentication
- `POST /system/v1/user/login` - User login with credentials
- `POST /system/v1/user/login_verify` - Verify login OTP
- `POST /system/v1/user/validate_otp` - Validate authentication OTP
- `GET|POST /system/v1/user/resend_otp/{user_id}` - Resend OTP

#### Account Management
- `POST /system/v1/user/account_create` - Create new user account
- `GET|POST /system/v1/user/account_edit/{user_id}` - Edit user account
- `POST /system/v1/user/password_reset` - Reset user password
- `POST /system/v1/user/password_change` - Change user password

#### Role & Permission Management
- `GET /system/v1/roles` - Get user roles
- `POST /system/v1/role/create` - Create new role
- `POST /system/v1/role/update/{roleId}` - Update role
- `GET /system/v1/permissions` - Get permissions
- `POST /system/v1/permission/create` - Create permission
- `POST /system/v1/permission/update/{permissionId}` - Update permission

#### System Users
- `GET|POST /system/v1/users` - Get system users
- `POST /system/v1/users/edit/{user_id}` - Edit system user
- `POST /system/v1/users/blacklist/{user_id}` - Blacklist user

### Dashboard Analytics (`/dashboard/`)

#### Core Dashboard
- `GET|POST /dashboard/v1/view` - Main dashboard statistics
- `GET|POST /dashboard/v1/summary` - Summary reports
- `GET|POST /dashboard/v1/summary/averages` - Average reports
- `GET|POST /dashboard/v1/game/summary` - Game summary statistics

#### Specialized Reports
- `GET|POST /dashboard/v1/greener_monday` - Greener Monday reports
- `GET|POST /dashboard/v1/sports_bets_summary` - Sports betting summary
- `GET|POST /dashboard/v1/virtual_bets_summary` - Virtual betting summary
- `GET|POST /dashboard/v1/softgames_bets_summary` - SoftGaming summary
- `GET|POST /dashboard/v1/charts` - Enhanced dashboard charts

### Betting Management (`/bets/`)

#### Bet Retrieval
- `GET|POST /bets/v1/sports` - Sports betting data
- `GET|POST /bets/v1/virtual` - Virtual betting data
- `GET|POST /bets/v1/soft_gaming` - SoftGaming betting data
- `GET|POST /bets/v1/casino/{typeId}` - Casino betting data

#### Bet Operations
- `GET|POST /bets/v1/resettle/{betId}` - Resettle specific bet
- `GET|POST /bets/v1/bet_slip` - Sports bet slips
- `GET|POST /bets/v1/partners_bet_slips` - Partner bet slips

#### Bet Management
- `GET|POST /bets/v1/audits` - Bet audit logs
- `GET|POST /bets/v1/audits/update/{id}` - Update bet audit
- `GET|POST /bets/v1/limits` - Betting limits
- `GET|POST /bets/v1/limits/update/{id}` - Update betting limits
- `GET|POST /bets/v1/games` - Betting games
- `GET|POST /bets/v1/games/update/{id}` - Update betting games

#### Security & Monitoring
- `GET|POST /bets/v1/blocked_ips` - Blocked IP addresses
- `GET|POST /bets/v1/blocked_ips/update/{id}` - Update blocked IPs
- `GET|POST /bets/v1/blocked_ips/profiles` - Blocked IP profiles
- `GET|POST /bets/v1/partner_balance` - Partner balance information

### Partner Management (`/partners/`)

#### Partner Operations
- `GET|POST /partners/v1/view` - View partners
- `POST /partners/v1/create` - Create new partner
- `POST /partners/v1/update/{partnerId}` - Update partner
- `POST /partners/v1/delete/{partnerId}` - Delete partner

#### Partner Services
- `GET|POST /partners/v1/partner_services` - Partner services
- `POST /partners/v1/service/create` - Create partner service
- `POST /partners/v1/service/update/{serviceId}` - Update partner service

#### Partner Data & Reports
- `GET|POST /partners/v1/partners_bets` - Partner betting data
- `GET|POST /partners/v1/partners_bet_slips` - Partner bet slips
- `GET|POST /partners/v1/partner_balance` - Partner balance
- `GET|POST /partners/v1/partner_balance_transactions` - Balance transactions
- `GET|POST /partners/v1/partner_balance_summary` - Balance summary

### Transaction Management (`/trxns/`)

#### Transaction Operations
- `GET|POST /trxns/v1/all` - All transactions
- `GET|POST /trxns/v1/deposits` - Deposit transactions
- `GET|POST /trxns/v1/withdrawals` - Withdrawal transactions

#### Approval Workflows
- `GET|POST /trxns/v1/wallet-approvals` - Wallet approvals
- `GET|POST /trxns/v1/approval-risk/{trxId}` - Risk approvals
- `GET|POST /trxns/v1/approve` - Approve wallet transactions
- `GET|POST /trxns/v1/upload/risk_approvals` - Upload risk approvals

### Reports (`/reports/`)

#### Communication Reports
- `GET|POST /reports/v1/list/outbox` - SMS outbox
- `GET|POST /reports/v1/list/blast_sms` - Blast SMS outbox
- `GET|POST /reports/v1/list/sms_filters` - SMS filters
- `GET|POST /reports/v1/list/referrals` - Referral reports

#### Financial Reports
- `GET|POST /reports/v1/list/paybills` - Paybill reports
- `GET|POST /reports/v1/list/channels` - Auth channels
- `GET|POST /reports/v1/list/taxes` - KRA taxes
- `GET|POST /reports/v1/list/tax_summary` - Tax summary
- `GET|POST /reports/v1/list/tax_payments` - Tax payments
- `POST|GET /reports/v1/tax/repay` - Tax repay API

#### Special Reports
- `GET|POST /reports/v1/special_reports` - Special reports

### Account Administration (`/adm/`)

#### Paybill Management
- `POST /adm/v1/paybill_create` - Create paybill entry
- `POST /adm/v1/paybill_edit/{paybillId}` - Edit paybill
- `POST /adm/v1/paybill_enable/{paybillId}` - Enable/disable paybill
- `POST /adm/v1/paybill_ipaddress/{paybillId}` - Manage paybill IPs
- `GET|POST /adm/v1/paybill_pull/{paybillId}` - Register paybill pull service

#### Reconciliation
- `POST /adm/v1/depost/recon` - Create reconciliation
- `POST /adm/v1/deposit/manual` - Manual deposit reconcile

#### Payout Management
- `POST /adm/v1/payouts/route/{paybillId}` - Enable/disable payout routing
- `POST /adm/v1/payouts/repost` - Payouts callback repost

#### Channel Management
- `POST /adm/v1/channel_create` - Create auth channel
- `POST /adm/v1/channel_edit/{channelId}` - Edit auth channel

## Authentication Implementation

### Request Headers
All authenticated endpoints require these headers:
```
X-Access-Token: JWT token
X-App-Key: Application key
X-Hash-Key: HMAC signature
X-Timestamp: Unix timestamp
```

### Authentication Flow
1. **Header Extraction**: Extract authentication headers
2. **Timestamp Validation**: Prevent replay attacks (5-minute window)
3. **Hash Validation**: Verify request integrity using HMAC
4. **JWT Verification**: Validate access token
5. **Permission Check**: Verify user permissions for endpoint

### Security Layers
- **Layer 1**: Request signature validation (HMAC)
- **Layer 2**: JWT token verification
- **Layer 3**: Permission-based authorization
- **Layer 4**: IP whitelisting (partner-specific)
- **Layer 5**: Rate limiting per endpoint

## Database Architecture

### Connection Management
- **Write Database**: Master for all write operations
- **Read Database**: Replicas for read operations
- **Connection Pooling**: Optimized connection management

### Key Tables
- **Users & Authentication**: user, user_login, user_roles, permissions
- **Partners**: partners, partner_services, partner_balance
- **Betting**: sports_bet, virtual_bet, casino_bet, bet_audits
- **Transactions**: transaction_summary, deposits, withdrawals
- **System**: audit_logs, blocked_ips, paybills

## Caching Strategy

### Redis Implementation
- **Session Management**: User session storage
- **Data Caching**: Frequently accessed data
- **Rate Limiting**: Request throttling counters
- **Queue Management**: Background job processing

### Cache Levels
- **L1**: Application-level caching
- **L2**: Redis distributed cache
- **L3**: Database query result cache

## Error Handling

### Response Format
```json
{
  "statusDescription": "Request status",
  "code": 200,
  "message": "Success/Error message",
  "data": {},
  "error": {
    "code": 400,
    "message": "Detailed error message"
  }
}
```

### Error Codes
- **200**: Success
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **429**: Rate Limited
- **500**: Internal Server Error

## Performance Optimization

### Query Optimization
- **Prepared Statements**: Prevent SQL injection
- **Indexed Queries**: Optimized database performance
- **Pagination**: Efficient data retrieval
- **Count Optimization**: Smart counting for large datasets

### Caching Strategy
- **TTL Configuration**: Appropriate cache expiration
- **Cache Invalidation**: Smart cache clearing
- **Memory Management**: Efficient memory usage

## Monitoring & Logging

### Logging Levels
- **Info**: Request/response logging
- **Warning**: Non-critical issues
- **Error**: Application errors
- **Emergency**: Critical system failures

### Audit Trail
- **User Actions**: Complete user activity log
- **System Events**: System-level event tracking
- **Security Events**: Authentication and authorization logs
- **Performance Metrics**: Response times and error rates

## Deployment Configuration

### Environment Variables
- Database connections (read/write)
- Redis configuration
- JWT secrets
- API keys and security tokens
- Rate limiting configurations

### Health Checks
- Database connectivity
- Redis availability
- External service status
- System resource monitoring

---

**Last Updated**: 2025-01-13
**Version**: 1.0.0
**Maintainer**: Development Team
