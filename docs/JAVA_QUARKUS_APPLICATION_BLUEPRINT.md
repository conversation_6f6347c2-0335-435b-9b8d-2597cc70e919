# Java Quarkus Application Blueprint

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture Design](#architecture-design)
3. [Security Implementation](#security-implementation)
4. [Redis Integration](#redis-integration)
5. [Queue & Messaging](#queue--messaging)
6. [Project Setup](#project-setup)
7. [Core Components](#core-components)
8. [API Design](#api-design)
9. [Testing Strategy](#testing-strategy)
10. [Deployment Guide](#deployment-guide)

## Project Overview

This blueprint provides a comprehensive guide for building a modern Java application using Quarkus framework with enterprise-grade security, Redis caching, message queuing, and real-time messaging capabilities.

### Key Features
- **Quarkus Framework**: Cloud-native, supersonic, subatomic Java
- **Security**: JWT authentication, RBAC, CORS, and security headers
- **Redis Integration**: Caching, session management, and distributed locks
- **Message Queuing**: RabbitMQ/Apache Kafka for asynchronous processing
- **Real-time Messaging**: WebSocket support for live updates
- **Database**: PostgreSQL with Hibernate ORM and connection pooling
- **Monitoring**: Health checks, metrics, and distributed tracing
- **Cloud-Ready**: Docker containerization and Kubernetes deployment

### Technology Stack
- **Framework**: Quarkus 3.x
- **Security**: Quarkus Security JWT, RBAC
- **Database**: PostgreSQL + Hibernate ORM
- **Caching**: Redis with Quarkus Redis extension
- **Messaging**: RabbitMQ/Kafka with SmallRye Reactive Messaging
- **WebSocket**: Quarkus WebSocket extension
- **Testing**: JUnit 5, TestContainers, REST Assured
- **Build**: Maven/Gradle
- **Containerization**: Docker + GraalVM native compilation

## Architecture Design

### Layered Architecture
```
┌─────────────────────────────────────────┐
│              API Layer                  │
│  (REST Controllers, WebSocket Endpoints)│
├─────────────────────────────────────────┤
│            Security Layer               │
│     (JWT, RBAC, Authentication)         │
├─────────────────────────────────────────┤
│            Service Layer                │
│    (Business Logic, Validation)         │
├─────────────────────────────────────────┤
│           Repository Layer              │
│      (Data Access, ORM Mapping)         │
├─────────────────────────────────────────┤
│          Infrastructure Layer           │
│  (Redis, Messaging, External Services)  │
└─────────────────────────────────────────┘
```

### Microservices Architecture
- **User Service**: Authentication, authorization, user management
- **Partner Service**: Partner management, balance tracking
- **Betting Service**: Bet processing, slip management
- **Notification Service**: Real-time notifications, messaging
- **Analytics Service**: Dashboard data, reporting

## Security Implementation

### JWT Authentication
```java
@ApplicationScoped
public class JWTService {
    
    @ConfigProperty(name = "jwt.secret")
    String jwtSecret;
    
    @ConfigProperty(name = "jwt.expiration")
    Long jwtExpiration;
    
    public String generateToken(UserPrincipal user) {
        return Jwt.issuer("mossbets-api")
                .upn(user.getUsername())
                .groups(user.getRoles())
                .claim("user_id", user.getId())
                .claim("partner_count", user.getPartnerCount())
                .expiresAt(Instant.now().plusSeconds(jwtExpiration))
                .sign();
    }
    
    public boolean validateToken(String token) {
        try {
            JsonWebToken jwt = parser.parse(token);
            return jwt.getExpirationTime() > Instant.now().getEpochSecond();
        } catch (Exception e) {
            return false;
        }
    }
}
```

### Role-Based Access Control
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true)
    private String username;
    
    @JsonIgnore
    private String password;
    
    @Enumerated(EnumType.STRING)
    private UserStatus status;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "user_roles")
    private Set<Role> roles = new HashSet<>();
    
    @ManyToMany
    @JoinTable(name = "user_partners")
    private Set<Partner> partners = new HashSet<>();
}

@RolesAllowed({"ADMIN", "PARTNER_MANAGER"})
@GET
@Path("/partners")
public Response getPartners(@Context SecurityContext securityContext) {
    UserPrincipal user = (UserPrincipal) securityContext.getUserPrincipal();
    return Response.ok(partnerService.getPartnersForUser(user.getId())).build();
}
```

### Security Headers & CORS
```java
@ApplicationScoped
public class SecurityHeadersFilter implements ContainerResponseFilter {
    
    @Override
    public void filter(ContainerRequestContext requestContext, 
                      ContainerResponseContext responseContext) {
        MultivaluedMap<String, Object> headers = responseContext.getHeaders();
        
        headers.add("X-Content-Type-Options", "nosniff");
        headers.add("X-Frame-Options", "DENY");
        headers.add("X-XSS-Protection", "1; mode=block");
        headers.add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        headers.add("Content-Security-Policy", "default-src 'self'");
    }
}
```

## Redis Integration

### Configuration
```properties
# Redis Configuration
quarkus.redis.hosts=redis://localhost:6379
quarkus.redis.password=${REDIS_PASSWORD:}
quarkus.redis.database=0
quarkus.redis.timeout=10s
quarkus.redis.max-pool-size=20
quarkus.redis.max-pool-waiting=30
```

### Caching Service
```java
@ApplicationScoped
public class CacheService {
    
    @Inject
    RedisDataSource redis;
    
    private static final String USER_SESSION_PREFIX = "user:session:";
    private static final String PARTNER_BALANCE_PREFIX = "partner:balance:";
    
    public void cacheUserSession(String token, UserSession session) {
        String key = USER_SESSION_PREFIX + token;
        redis.value(String.class, UserSession.class)
             .setex(key, Duration.ofMinutes(60), session);
    }
    
    public Optional<UserSession> getUserSession(String token) {
        String key = USER_SESSION_PREFIX + token;
        return Optional.ofNullable(
            redis.value(String.class, UserSession.class).get(key)
        );
    }
    
    public void cachePartnerBalance(Long partnerId, PartnerBalance balance) {
        String key = PARTNER_BALANCE_PREFIX + partnerId;
        redis.value(String.class, PartnerBalance.class)
             .setex(key, Duration.ofMinutes(30), balance);
    }
    
    public void invalidatePartnerBalance(Long partnerId) {
        String key = PARTNER_BALANCE_PREFIX + partnerId;
        redis.key().del(key);
    }
}
```

### Distributed Locking
```java
@ApplicationScoped
public class DistributedLockService {
    
    @Inject
    RedisDataSource redis;
    
    public boolean acquireLock(String lockKey, String lockValue, Duration expiration) {
        return redis.value(String.class)
                   .setnx(lockKey, lockValue, expiration);
    }
    
    public void releaseLock(String lockKey, String lockValue) {
        String script = """
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
            """;
        redis.execute(script, List.of(lockKey), List.of(lockValue));
    }
    
    @Transactional
    public void updatePartnerBalanceWithLock(Long partnerId, BigDecimal amount) {
        String lockKey = "lock:partner:balance:" + partnerId;
        String lockValue = UUID.randomUUID().toString();
        
        if (acquireLock(lockKey, lockValue, Duration.ofSeconds(30))) {
            try {
                // Update balance logic
                partnerService.updateBalance(partnerId, amount);
                cacheService.invalidatePartnerBalance(partnerId);
            } finally {
                releaseLock(lockKey, lockValue);
            }
        } else {
            throw new ConcurrentModificationException("Unable to acquire lock for partner balance update");
        }
    }
}
```

## Queue & Messaging

### RabbitMQ Configuration
```properties
# RabbitMQ Configuration
rabbitmq-host=localhost
rabbitmq-port=5672
rabbitmq-username=${RABBITMQ_USERNAME:guest}
rabbitmq-password=${RABBITMQ_PASSWORD:guest}

# Messaging Channels
mp.messaging.outgoing.bet-processing.connector=smallrye-rabbitmq
mp.messaging.outgoing.bet-processing.exchange.name=bet-exchange
mp.messaging.outgoing.bet-processing.routing-key=bet.processing

mp.messaging.incoming.bet-results.connector=smallrye-rabbitmq
mp.messaging.incoming.bet-results.queue.name=bet-results-queue
mp.messaging.incoming.bet-results.exchange.name=bet-exchange
mp.messaging.incoming.bet-results.routing-key=bet.results
```

### Message Producers
```java
@ApplicationScoped
public class BetMessageProducer {
    
    @Channel("bet-processing")
    Emitter<BetProcessingMessage> betProcessingEmitter;
    
    @Channel("partner-notifications")
    Emitter<PartnerNotification> partnerNotificationEmitter;
    
    public void sendBetForProcessing(BetSlip betSlip) {
        BetProcessingMessage message = BetProcessingMessage.builder()
            .betId(betSlip.getId())
            .partnerId(betSlip.getPartnerId())
            .amount(betSlip.getAmount())
            .timestamp(Instant.now())
            .build();
            
        betProcessingEmitter.send(message);
    }
    
    public void notifyPartnerBalanceUpdate(Long partnerId, BigDecimal newBalance) {
        PartnerNotification notification = PartnerNotification.builder()
            .partnerId(partnerId)
            .type(NotificationType.BALANCE_UPDATE)
            .data(Map.of("new_balance", newBalance))
            .timestamp(Instant.now())
            .build();
            
        partnerNotificationEmitter.send(notification);
    }
}
```

### Message Consumers
```java
@ApplicationScoped
public class BetMessageConsumer {
    
    @Inject
    BetService betService;
    
    @Inject
    PartnerService partnerService;
    
    @Incoming("bet-results")
    @Acknowledgment(Acknowledgment.Strategy.POST_PROCESSING)
    public CompletionStage<Void> processBetResults(BetResultMessage message) {
        return CompletableFuture.runAsync(() -> {
            try {
                betService.updateBetResult(message.getBetId(), message.getResult());
                
                if (message.getResult() == BetResult.WON) {
                    partnerService.creditWinnings(
                        message.getPartnerId(), 
                        message.getWinAmount()
                    );
                }
                
                // Send real-time notification
                webSocketService.notifyPartner(
                    message.getPartnerId(), 
                    "Bet result updated: " + message.getResult()
                );
                
            } catch (Exception e) {
                log.error("Error processing bet result: {}", message, e);
                throw new RuntimeException(e);
            }
        });
    }
    
    @Incoming("partner-balance-updates")
    public void handlePartnerBalanceUpdate(PartnerBalanceUpdateMessage message) {
        // Update cache
        cacheService.invalidatePartnerBalance(message.getPartnerId());
        
        // Send WebSocket notification
        webSocketService.broadcastBalanceUpdate(message);
    }
}
```
