# System Architecture Blueprint

## Overview
This document provides a comprehensive blueprint for recreating the MossBets B2B Back Office application architecture in Node.js and Quarkus frameworks, with detailed security features, Redis integration, queueing, and messaging systems.

## Current System Architecture (Phalcon PHP)

### Core Components
- **Framework**: Phalcon PHP 4.x
- **Architecture**: Micro-services with MicroCollection routing
- **Authentication**: JWT-based with multi-layer validation
- **Database**: MySQL with read/write separation
- **Caching**: Redis for session management and caching
- **Security**: Multi-factor authentication, IP whitelisting, hash validation

### Security Features Implemented

#### 1. Authentication & Authorization
```php
// Current implementation pattern
$authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
```

**Security Layers:**
- **Access Token Validation**: JWT token verification
- **Hash Validation**: Request integrity using HMAC
- **Timestamp Validation**: Prevents replay attacks
- **Permission-based Authorization**: Role-based access control (RBAC)
- **IP Whitelisting**: Partner-specific IP restrictions

#### 2. Request Validation
- **Header Extraction**: Standardized authentication header processing
- **Data Sanitization**: Input validation and sanitization
- **Rate Limiting**: Request throttling per endpoint
- **CORS Configuration**: Cross-origin request security

#### 3. Error Handling
- **Centralized Error Responses**: Consistent error format
- **Security-aware Logging**: Audit trail without sensitive data exposure
- **Graceful Degradation**: Fallback mechanisms

## Node.js Implementation Blueprint

### Technology Stack
```json
{
  "framework": "Express.js / Fastify",
  "authentication": "Passport.js + JWT",
  "database": "Prisma ORM + MySQL",
  "caching": "Redis + ioredis",
  "validation": "Joi / Zod",
  "security": "Helmet.js + express-rate-limit",
  "messaging": "Bull Queue + Redis",
  "monitoring": "Winston + Morgan"
}
```

### Project Structure
```
src/
├── controllers/
│   ├── AuthController.js
│   ├── DashboardController.js
│   ├── BetsController.js
│   ├── UserController.js
│   ├── PartnerController.js
│   └── TransactionController.js
├── middleware/
│   ├── auth.js
│   ├── validation.js
│   ├── rateLimit.js
│   └── security.js
├── services/
│   ├── AuthService.js
│   ├── CacheService.js
│   ├── QueueService.js
│   └── NotificationService.js
├── models/
├── routes/
├── utils/
└── config/
```

### Authentication Implementation

#### Middleware Setup
```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const redis = require('../config/redis');

class AuthMiddleware {
  static async authenticateAndAuthorize(req, res, next) {
    try {
      // Extract authentication headers
      const authData = this.extractAuthHeaders(req.headers);
      
      // Validate timestamp (prevent replay attacks)
      if (!this.validateTimestamp(authData.timestamp)) {
        return res.status(401).json({
          code: 401,
          message: 'Request timestamp expired'
        });
      }
      
      // Validate hash integrity
      if (!this.validateHash(req.body, authData)) {
        return res.status(401).json({
          code: 401,
          message: 'Invalid request signature'
        });
      }
      
      // Verify JWT token
      const decoded = jwt.verify(authData.accessToken, process.env.JWT_SECRET);
      
      // Check user permissions
      const hasPermission = await this.validateUserPermission(
        decoded.userId, 
        req.route.permission
      );
      
      if (!hasPermission) {
        return res.status(403).json({
          code: 403,
          message: 'Insufficient permissions'
        });
      }
      
      req.user = decoded;
      next();
    } catch (error) {
      return res.status(401).json({
        code: 401,
        message: 'Authentication failed'
      });
    }
  }
  
  static extractAuthHeaders(headers) {
    return {
      accessToken: headers['x-access-token'],
      appKey: headers['x-app-key'],
      hashKey: headers['x-hash-key'],
      timestamp: headers['x-timestamp']
    };
  }
  
  static validateTimestamp(timestamp) {
    const now = Math.floor(Date.now() / 1000);
    const requestTime = parseInt(timestamp);
    return Math.abs(now - requestTime) <= 300; // 5 minutes tolerance
  }
  
  static validateHash(data, authData) {
    const payload = JSON.stringify(data) + authData.timestamp;
    const expectedHash = crypto
      .createHmac('sha256', authData.hashKey)
      .update(payload)
      .digest('hex');
    return expectedHash === authData.signature;
  }
}

module.exports = AuthMiddleware;
```

### Redis Integration
```javascript
// services/CacheService.js
const Redis = require('ioredis');

class CacheService {
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3
    });
  }
  
  async set(key, value, ttl = 3600) {
    return await this.redis.setex(key, ttl, JSON.stringify(value));
  }
  
  async get(key) {
    const value = await this.redis.get(key);
    return value ? JSON.parse(value) : null;
  }
  
  async invalidate(pattern) {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      return await this.redis.del(...keys);
    }
  }
}

module.exports = new CacheService();
```

### Queue System Implementation
```javascript
// services/QueueService.js
const Queue = require('bull');
const redis = require('../config/redis');

class QueueService {
  constructor() {
    this.queues = {
      notifications: new Queue('notifications', { redis }),
      reports: new Queue('reports', { redis }),
      transactions: new Queue('transactions', { redis })
    };
    
    this.setupProcessors();
  }
  
  setupProcessors() {
    this.queues.notifications.process('email', require('../processors/emailProcessor'));
    this.queues.notifications.process('sms', require('../processors/smsProcessor'));
    this.queues.reports.process('generate', require('../processors/reportProcessor'));
  }
  
  async addJob(queueName, jobType, data, options = {}) {
    return await this.queues[queueName].add(jobType, data, {
      delay: options.delay || 0,
      attempts: options.attempts || 3,
      backoff: 'exponential'
    });
  }
}

module.exports = new QueueService();
```

## Quarkus Implementation Blueprint

### Technology Stack
```xml
<!-- pom.xml dependencies -->
<dependencies>
  <dependency>
    <groupId>io.quarkus</groupId>
    <artifactId>quarkus-resteasy-reactive-jackson</artifactId>
  </dependency>
  <dependency>
    <groupId>io.quarkus</groupId>
    <artifactId>quarkus-security-jpa</artifactId>
  </dependency>
  <dependency>
    <groupId>io.quarkus</groupId>
    <artifactId>quarkus-redis-client</artifactId>
  </dependency>
  <dependency>
    <groupId>io.quarkus</groupId>
    <artifactId>quarkus-hibernate-orm-panache</artifactId>
  </dependency>
  <dependency>
    <groupId>io.quarkus</groupId>
    <artifactId>quarkus-smallrye-jwt</artifactId>
  </dependency>
</dependencies>
```

### Project Structure
```
src/main/java/
├── controllers/
│   ├── AuthController.java
│   ├── DashboardController.java
│   ├── BetsController.java
│   └── UserController.java
├── security/
│   ├── AuthenticationFilter.java
│   ├── AuthorizationService.java
│   └── SecurityUtils.java
├── services/
│   ├── CacheService.java
│   ├── QueueService.java
│   └── NotificationService.java
├── entities/
├── dto/
└── config/
```

### Authentication Implementation
```java
// security/AuthenticationFilter.java
@Provider
@Priority(Priorities.AUTHENTICATION)
public class AuthenticationFilter implements ContainerRequestFilter {
    
    @Inject
    AuthorizationService authService;
    
    @Inject
    CacheService cacheService;
    
    @Override
    public void filter(ContainerRequestContext requestContext) throws IOException {
        String path = requestContext.getUriInfo().getPath();
        
        if (isPublicEndpoint(path)) {
            return;
        }
        
        try {
            AuthData authData = extractAuthHeaders(requestContext.getHeaders());
            
            // Validate timestamp
            if (!validateTimestamp(authData.getTimestamp())) {
                abortWithError(requestContext, 401, "Request timestamp expired");
                return;
            }
            
            // Validate hash
            if (!validateHash(requestContext, authData)) {
                abortWithError(requestContext, 401, "Invalid request signature");
                return;
            }
            
            // Verify JWT and permissions
            JsonWebToken jwt = authService.verifyToken(authData.getAccessToken());
            String permission = getRequiredPermission(path);
            
            if (!authService.hasPermission(jwt.getSubject(), permission)) {
                abortWithError(requestContext, 403, "Insufficient permissions");
                return;
            }
            
            // Set security context
            requestContext.setSecurityContext(new CustomSecurityContext(jwt));
            
        } catch (Exception e) {
            abortWithError(requestContext, 401, "Authentication failed");
        }
    }
}
```

### Redis Integration (Quarkus)
```java
// services/CacheService.java
@ApplicationScoped
public class CacheService {
    
    @Inject
    RedisClient redisClient;
    
    public Uni<Void> set(String key, Object value, Duration ttl) {
        return redisClient.setex(key, ttl.getSeconds(), Json.encode(value));
    }
    
    public Uni<Optional<JsonObject>> get(String key) {
        return redisClient.get(key)
            .map(response -> response != null ? 
                Optional.of(new JsonObject(response.toString())) : 
                Optional.empty());
    }
    
    public Uni<Void> invalidate(String pattern) {
        return redisClient.keys(pattern)
            .chain(keys -> keys.isEmpty() ? 
                Uni.createFrom().voidItem() : 
                redisClient.del(keys.toArray(new String[0])).replaceWithVoid());
    }
}
```

## Security Features Deep Dive

### 1. Multi-Layer Authentication
- **Layer 1**: Request signature validation (HMAC)
- **Layer 2**: JWT token verification
- **Layer 3**: Permission-based authorization
- **Layer 4**: IP whitelisting (partner-specific)
- **Layer 5**: Rate limiting per endpoint

### 2. Data Protection
- **Encryption at Rest**: Database field-level encryption
- **Encryption in Transit**: TLS 1.3 for all communications
- **PII Masking**: Sensitive data masking in logs
- **Data Retention**: Automated data purging policies

### 3. Audit & Monitoring
- **Request Logging**: Comprehensive audit trail
- **Security Events**: Real-time security monitoring
- **Performance Metrics**: Response time and error rate tracking
- **Alerting**: Automated security incident alerts

## Messaging & Queue Architecture

### Message Types
1. **Transactional Messages**: Payment processing, bet settlements
2. **Notification Messages**: Email, SMS, push notifications
3. **Reporting Messages**: Report generation, data exports
4. **System Messages**: Health checks, maintenance alerts

### Queue Configuration
```javascript
// Queue priorities and processing
const queueConfig = {
  transactions: { priority: 'high', concurrency: 10 },
  notifications: { priority: 'medium', concurrency: 5 },
  reports: { priority: 'low', concurrency: 2 },
  system: { priority: 'critical', concurrency: 1 }
};
```

## Database Architecture

### Read/Write Separation
- **Write DB**: Master database for all write operations
- **Read DB**: Replica databases for read operations
- **Connection Pooling**: Optimized connection management
- **Query Optimization**: Indexed queries and caching strategies

### Data Models
- **Users & Permissions**: RBAC implementation
- **Partners & Services**: Multi-tenant architecture
- **Transactions**: Immutable transaction logs
- **Bets & Games**: Real-time betting data
- **Audit Logs**: Comprehensive activity tracking

## Deployment & Infrastructure

### Containerization
```dockerfile
# Node.js Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["node", "server.js"]
```

### Kubernetes Configuration
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mossbets-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mossbets-api
  template:
    metadata:
      labels:
        app: mossbets-api
    spec:
      containers:
      - name: api
        image: mossbets/api:latest
        ports:
        - containerPort: 3000
        env:
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
```

This blueprint provides a comprehensive foundation for recreating the system in modern frameworks while maintaining all security features and architectural patterns.
