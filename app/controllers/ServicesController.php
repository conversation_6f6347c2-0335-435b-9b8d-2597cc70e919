<?php

require_once 'app/utils/ControllerHelpers.php';

class ServicesController extends \ControllerBase
{
    /**
     * GetServices
     * @return type
     */
    function GetServices()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Services";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'service_type', 'status', 'partner_id'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $userId = $authResult['user_id'];

            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['service_type']) {
                $searchQuery .= " AND s.service_type = :service_type";
                $queryParams[':service_type'] = $params['service_type'];
            }

            if ($params['status'] !== false) {
                $searchQuery .= " AND s.status = :status";
                $queryParams[':status'] = $params['status'];
            }

            if ($params['partner_id']) {
                $searchQuery .= " AND s.partner_id = :partner_id";
                $queryParams[':partner_id'] = $params['partner_id'];
            }

            // Build sorting
            $sorting = $this->tableQueryBuilder($searchParams['sort'], $searchParams['order'], 
                $pagination['page'], $pagination['limit']);

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query - assuming a services table exists
            $query = "SELECT (SELECT COUNT(s.id) FROM services s $searchQuery) as trx_count,
                      s.id, s.service_name, s.service_type, s.partner_id, p.name as partner_name,
                      s.endpoint_url, s.api_key, s.status, s.created_at, s.updated_at
                      FROM services s 
                      LEFT JOIN partners p ON s.partner_id = p.id 
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);
            
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No services found!'], true);
            }

            // Mask sensitive data
            foreach ($results as &$result) {
                if (isset($result['api_key'])) {
                    $result['api_key'] = $this->maskApiKey($result['api_key']);
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' Services successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreateService
     * @return type
     */
    function CreateService()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Service";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);
            
            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'service_name', 'service_type', 'partner_id', 'endpoint_url', 'api_key', 'status'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);
            
            // Validate authentication
            $authValidation = ControllerHelpers::validateAuthParams($authData, ['timestamp']);
            if (!$authValidation['valid']) {
                $errorResponse = ControllerHelpers::buildAuthErrorResponse(__CLASS__, __LINE__, $authValidation['missing_fields']);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'], 
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Validate required fields
            if (!$params['service_name'] || !$params['service_type'] || !$params['partner_id']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => "Service name, type, and partner ID are required!"], true);
            }

            // Authenticate user
            $authResponse = UserUtils::QuickAuthenticate($authData['access_token']);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            // Check permissions
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Check if service already exists
            $existingService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM services WHERE service_name = :service_name AND partner_id = :partner_id", 
                [':service_name' => $params['service_name'], ':partner_id' => $params['partner_id']]);

            if ($existingService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 409, 'message' => "Service with this name already exists for this partner!"], true);
            }

            // Insert new service
            $serviceId = $this->rawInsertBulk('dbUser', 'services', [
                'service_name' => $params['service_name'],
                'service_type' => $params['service_type'],
                'partner_id' => $params['partner_id'],
                'endpoint_url' => $params['endpoint_url'] ?: null,
                'api_key' => $params['api_key'] ? $this->Encrypt($params['api_key']) : null,
                'status' => $params['status'] !== false ? $params['status'] : 1,
                'created_at' => $this->now(),
                'updated_at' => $this->now()
            ]);

            if (!$serviceId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => "Failed to create service!"], true);
            }

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Service created successfully!',
                    'data' => ['service_id' => $serviceId]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdateService
     * @param int $serviceId
     * @return type
     */
    function UpdateService($serviceId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Service";

        $data = (array)$this->request->getJsonRawBody();

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);
            
            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'service_name', 'service_type', 'endpoint_url', 'api_key', 'status'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);
            
            // Validate authentication
            $authValidation = ControllerHelpers::validateAuthParams($authData, ['timestamp']);
            if (!$authValidation['valid']) {
                $errorResponse = ControllerHelpers::buildAuthErrorResponse(__CLASS__, __LINE__, $authValidation['missing_fields']);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'], 
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Authenticate user
            $authResponse = UserUtils::QuickAuthenticate($authData['access_token']);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            // Check permissions
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Check if service exists
            $existingService = $this->rawSelectOneRecord('dbUser',
                "SELECT id, service_name FROM services WHERE id = :id", [':id' => $serviceId]);

            if (!$existingService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => "Service not found!"], true);
            }

            // Build update fields
            $fields = [];
            $updateParams = [':id' => $serviceId];

            if ($params['service_name']) {
                $fields[] = "service_name = :service_name";
                $updateParams[':service_name'] = $params['service_name'];
            }

            if ($params['service_type']) {
                $fields[] = "service_type = :service_type";
                $updateParams[':service_type'] = $params['service_type'];
            }

            if ($params['endpoint_url']) {
                $fields[] = "endpoint_url = :endpoint_url";
                $updateParams[':endpoint_url'] = $params['endpoint_url'];
            }

            if ($params['api_key']) {
                $fields[] = "api_key = :api_key";
                $updateParams[':api_key'] = $this->Encrypt($params['api_key']);
            }

            if ($params['status'] !== false) {
                $fields[] = "status = :status";
                $updateParams[':status'] = $params['status'];
            }

            if (empty($fields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => "No valid fields provided for update!"], true);
            }

            $fields[] = "updated_at = :updated_at";
            $updateParams[':updated_at'] = $this->now();

            $sql = "UPDATE services SET " . implode(', ', $fields) . " WHERE id = :id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbUser', $sql, $updateParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => "Failed to update service!"], true);
            }

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Service updated successfully!'
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * Mask API key for security
     * @param string $apiKey
     * @return string
     */
    private function maskApiKey($apiKey)
    {
        if (strlen($apiKey) <= 8) {
            return str_repeat('*', strlen($apiKey));
        }
        return substr($apiKey, 0, 4) . str_repeat('*', strlen($apiKey) - 8) . substr($apiKey, -4);
    }
}
