<?php

require_once 'app/utils/ControllerHelpers.php';

class TransactionsController extends \ControllerBase
{

    /**
     * GetPartnerBalanceTransactions
     * @return type
     */
    function GetPartnerBalanceTransactions()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Balance Transactions";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'transaction_type', 'status'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Validate authentication
            $authValidation = ControllerHelpers::validateAuthParams($authData, ['timestamp']);
            if (!$authValidation['valid']) {
                $errorResponse = ControllerHelpers::buildAuthErrorResponse(__CLASS__, __LINE__, $authValidation['missing_fields']);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Authenticate user
            $authResponse = UserUtils::QuickAuthenticate($authData['access_token']);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            // Check permissions
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);
            $dateRange = ControllerHelpers::extractDateRange($data);

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $searchQuery .= " AND pbt.partner_id = :partner_id";
                $queryParams[':partner_id'] = $params['partner_id'];
            }

            if ($params['transaction_type']) {
                $searchQuery .= " AND pbt.transaction_type = :transaction_type";
                $queryParams[':transaction_type'] = $params['transaction_type'];
            }

            if ($params['status'] !== false) {
                $searchQuery .= " AND pbt.status = :status";
                $queryParams[':status'] = $params['status'];
            }

            if ($dateRange['start_date']) {
                $searchQuery .= " AND DATE(pbt.created_at) >= :start_date";
                $queryParams[':start_date'] = $dateRange['start_date'];
            }

            if ($dateRange['end_date']) {
                $searchQuery .= " AND DATE(pbt.created_at) <= :end_date";
                $queryParams[':end_date'] = $dateRange['end_date'];
            }

            // Build sorting
            $sorting = $this->tableQueryBuilder($searchParams['sort'], $searchParams['order'],
                $pagination['page'], $pagination['limit']);

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query - assuming a partner_balance_transactions table exists
            $query = "SELECT (SELECT COUNT(pbt.id) FROM partner_balance_transactions pbt $searchQuery) as trx_count,
                      pbt.id, pbt.partner_id, p.name as partner_name, pbt.transaction_type,
                      pbt.amount, pbt.balance_before, pbt.balance_after, pbt.reference_id,
                      pbt.description, pbt.status, pbt.created_at, pbt.updated_at,
                      pb.balance as current_balance, pb.bonus as current_bonus
                      FROM partner_balance_transactions pbt
                      LEFT JOIN partners p ON pbt.partner_id = p.id
                      LEFT JOIN partner_balance pb ON pbt.partner_id = pb.partner_id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner balance transactions found!'], true);
            }

            // Format numeric values
            foreach ($results as &$result) {
                $result['amount'] = number_format((float)$result['amount'], 2, '.', '');
                $result['balance_before'] = number_format((float)$result['balance_before'], 2, '.', '');
                $result['balance_after'] = number_format((float)$result['balance_after'], 2, '.', '');
                $result['current_balance'] = number_format((float)$result['current_balance'], 2, '.', '');
                $result['current_bonus'] = number_format((float)$result['current_bonus'], 2, '.', '');
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Balance Transactions successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerBalanceSummary
     * @return type
     */
    function GetPartnerBalanceSummary()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Balance Summary";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Validate authentication
            $authValidation = ControllerHelpers::validateAuthParams($authData, ['timestamp']);
            if (!$authValidation['valid']) {
                $errorResponse = ControllerHelpers::buildAuthErrorResponse(__CLASS__, __LINE__, $authValidation['missing_fields']);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Authenticate user
            $authResponse = UserUtils::QuickAuthenticate($authData['access_token']);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            // Check permissions
            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            $summaryData = [];

            if ($params['partner_id']) {
                // Get specific partner balance summary
                $partnerSummary = $this->rawSelectOneRecord('dbUser',
                    "SELECT p.id, p.name, pb.balance, pb.bonus, pb.status, pb.updated_at,
                            COUNT(pbt.id) as transaction_count,
                            SUM(CASE WHEN pbt.transaction_type = 'credit' THEN pbt.amount ELSE 0 END) as total_credits,
                            SUM(CASE WHEN pbt.transaction_type = 'debit' THEN pbt.amount ELSE 0 END) as total_debits
                     FROM partners p
                     LEFT JOIN partner_balance pb ON p.id = pb.partner_id
                     LEFT JOIN partner_balance_transactions pbt ON p.id = pbt.partner_id
                     WHERE p.id = :partner_id
                     GROUP BY p.id",
                    [':partner_id' => $params['partner_id']]);

                if ($partnerSummary) {
                    $partnerSummary['balance'] = number_format((float)$partnerSummary['balance'], 2, '.', '');
                    $partnerSummary['bonus'] = number_format((float)$partnerSummary['bonus'], 2, '.', '');
                    $partnerSummary['total_credits'] = number_format((float)$partnerSummary['total_credits'], 2, '.', '');
                    $partnerSummary['total_debits'] = number_format((float)$partnerSummary['total_debits'], 2, '.', '');
                    $summaryData = $partnerSummary;
                }
            } else {
                // Get overall summary
                $overallSummary = $this->rawSelectOneRecord('dbUser',
                    "SELECT COUNT(DISTINCT pb.partner_id) as total_partners,
                            SUM(pb.balance) as total_balance,
                            SUM(pb.bonus) as total_bonus,
                            AVG(pb.balance) as average_balance
                     FROM partner_balance pb
                     WHERE pb.status = 1", []);

                if ($overallSummary) {
                    $overallSummary['total_balance'] = number_format((float)$overallSummary['total_balance'], 2, '.', '');
                    $overallSummary['total_bonus'] = number_format((float)$overallSummary['total_bonus'], 2, '.', '');
                    $overallSummary['average_balance'] = number_format((float)$overallSummary['average_balance'], 2, '.', '');
                    $summaryData = $overallSummary;
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Partner balance summary retrieved successfully!',
                    'data' => $summaryData
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }
}
