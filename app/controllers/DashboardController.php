<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * Description of DashboardController
 *
 * <AUTHOR>
 */
class DashboardController extends \ControllerBase
{

    /**
     * ViewDashboardStats - Get comprehensive dashboard statistics
     * @return type
     */
    function ViewDashboardStats()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Dashboard Statistics";

        // Extract request data using ControllerHelper pattern
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request ViewDashboardStats:" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'period', 'export'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $period = $extractedData['period'] ?: '7days';
            $export = $extractedData['export'] ?: false;

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Get date condition for period filtering
            $dateCondition = $this->getDateCondition($period);

            // 1. User Statistics
            $userStats = $this->rawSelectOneRecord('dbUser',
                "SELECT
                    COUNT(*) as total_users,
                    SUM(CASE WHEN u.status = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN u.status = 2 THEN 1 ELSE 0 END) as inactive_users,
                    SUM(CASE WHEN u.status = 3 THEN 1 ELSE 0 END) as suspended_users,
                    SUM(CASE WHEN DATE(u.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_30days
                 FROM user u", []);

            // 2. Partner Statistics
            $partnerStats = $this->rawSelectOneRecord('dbUser',
                "SELECT
                    COUNT(*) as total_partners,
                    SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as active_partners,
                    SUM(pb.balance) as total_balance,
                    AVG(pb.balance) as average_balance,
                    MAX(pb.balance) as highest_balance,
                    SUM(CASE WHEN DATE(p.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_partners_30days
                 FROM partners p
                 LEFT JOIN partner_balance pb ON p.id = pb.partner_id AND pb.status = 1", []);

            // 3. Betting Statistics
            $bettingStats = $this->rawSelectOneRecord('dbBetsRead',
                "SELECT
                    COUNT(*) as total_bets,
                    SUM(bet_amount) as total_stakes,
                    SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END) as total_winnings,
                    (SUM(bet_amount) - SUM(CASE WHEN status = 1 THEN possible_win ELSE 0 END)) as net_revenue,
                    AVG(bet_amount) as average_stake,
                    COUNT(DISTINCT partner_id) as betting_partners
                 FROM partners_bets pb
                 WHERE status IN (0,1,2,3) $dateCondition", []);

            // 4. Transaction Statistics
            $transactionStats = $this->rawSelectOneRecord('dbTrxnRead',
                "SELECT
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN reference_type_id = 2 THEN amount ELSE 0 END) as total_deposits,
                    SUM(CASE WHEN reference_type_id = 1 THEN amount ELSE 0 END) as total_withdrawals,
                    COUNT(CASE WHEN reference_type_id = 2 THEN 1 END) as deposit_count,
                    COUNT(CASE WHEN reference_type_id = 1 THEN 1 END) as withdrawal_count,
                    AVG(CASE WHEN reference_type_id = 2 THEN amount END) as avg_deposit,
                    AVG(CASE WHEN reference_type_id = 1 THEN amount END) as avg_withdrawal
                 FROM transaction_summary ts
                 WHERE 1=1 $dateCondition", []);

            // 5. System Performance Statistics
            $systemStats = $this->rawSelectOneRecord('dbUser',
                "SELECT
                    COUNT(*) as total_roles,
                    (SELECT COUNT(*) FROM user_permissions WHERE status = 1) as total_permissions,
                    (SELECT COUNT(*) FROM auth_channels WHERE status = 1) as active_channels
                 FROM user_roles
                 WHERE status = 1", []);

            // Build comprehensive dashboard data
            $dashboardData = [
                'users' => [
                    'total' => (int)($userStats['total_users'] ?? 0),
                    'active' => (int)($userStats['active_users'] ?? 0),
                    'inactive' => (int)($userStats['inactive_users'] ?? 0),
                    'suspended' => (int)($userStats['suspended_users'] ?? 0),
                    'new_this_month' => (int)($userStats['new_users_30days'] ?? 0)
                ],
                'partners' => [
                    'total' => (int)($partnerStats['total_partners'] ?? 0),
                    'active' => (int)($partnerStats['active_partners'] ?? 0),
                    'total_balance' => number_format((float)($partnerStats['total_balance'] ?? 0), 2),
                    'average_balance' => number_format((float)($partnerStats['average_balance'] ?? 0), 2),
                    'highest_balance' => number_format((float)($partnerStats['highest_balance'] ?? 0), 2),
                    'new_this_month' => (int)($partnerStats['new_partners_30days'] ?? 0)
                ],
                'betting' => [
                    'total_bets' => (int)($bettingStats['total_bets'] ?? 0),
                    'total_stakes' => number_format((float)($bettingStats['total_stakes'] ?? 0), 2),
                    'total_winnings' => number_format((float)($bettingStats['total_winnings'] ?? 0), 2),
                    'net_revenue' => number_format((float)($bettingStats['net_revenue'] ?? 0), 2),
                    'average_stake' => number_format((float)($bettingStats['average_stake'] ?? 0), 2),
                    'betting_partners' => (int)($bettingStats['betting_partners'] ?? 0)
                ],
                'transactions' => [
                    'total' => (int)($transactionStats['total_transactions'] ?? 0),
                    'deposits' => [
                        'total_amount' => number_format((float)($transactionStats['total_deposits'] ?? 0), 2),
                        'count' => (int)($transactionStats['deposit_count'] ?? 0),
                        'average' => number_format((float)($transactionStats['avg_deposit'] ?? 0), 2)
                    ],
                    'withdrawals' => [
                        'total_amount' => number_format((float)($transactionStats['total_withdrawals'] ?? 0), 2),
                        'count' => (int)($transactionStats['withdrawal_count'] ?? 0),
                        'average' => number_format((float)($transactionStats['avg_withdrawal'] ?? 0), 2)
                    ]
                ],
                'system' => [
                    'total_roles' => (int)($systemStats['total_roles'] ?? 0),
                    'total_permissions' => (int)($systemStats['total_permissions'] ?? 0),
                    'active_channels' => (int)($systemStats['active_channels'] ?? 0),
                    'period' => $period
                ]
            ];

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Dashboard statistics retrieved successfully!',
                    'data' => $dashboardData
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetDashboardCharts
     * @return type
     */
    function GetDashboardCharts()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Dashboard Charts";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'chart_type', 'period'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Validate authentication
            $authValidation = ControllerHelpers::validateAuthParams($authData, ['timestamp']);
            if (!$authValidation['valid']) {
                $errorResponse = ControllerHelpers::buildAuthErrorResponse(__CLASS__, __LINE__, $authValidation['missing_fields']);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $chartData = [];
            $period = $params['period'] ?: '7days';
            $chartType = $params['chart_type'] ?: 'all';

            // Partner Balance Chart
            if ($chartType === 'all' || $chartType === 'partner_balance') {
                $balanceData = $this->rawSelect('dbUser',
                    "SELECT p.name as partner_name, pb.balance, pb.bonus
                     FROM partner_balance pb
                     JOIN partners p ON pb.partner_id = p.id
                     WHERE pb.status = 1
                     ORDER BY pb.balance DESC
                     LIMIT 10", []);

                if ($balanceData) {
                    $chartData['partner_balance'] = ControllerHelpers::buildChartData($balanceData, 'partner_name', 'balance');
                    $chartData['partner_balance']['title'] = 'Top 10 Partners by Balance';
                    $chartData['partner_balance']['type'] = 'doughnut';
                }
            }

            // Bets by Sport Chart
            if ($chartType === 'all' || $chartType === 'bets_by_sport') {
                $dateCondition = $this->getDateCondition($period);
                $sportsData = $this->rawSelect('dbBetsRead',
                    "SELECT s.sport_name, COUNT(sb.bet_id) as bet_count, SUM(sb.bet_amount) as total_amount
                     FROM sports_bet sb
                     JOIN sports s ON sb.sport_id = s.id
                     WHERE sb.status IN (0,1,2,3) $dateCondition
                     GROUP BY s.sport_name
                     ORDER BY bet_count DESC
                     LIMIT 8", []);

                if ($sportsData) {
                    $chartData['bets_by_sport'] = ControllerHelpers::buildChartData($sportsData, 'sport_name', 'bet_count');
                    $chartData['bets_by_sport']['title'] = 'Bets by Sport (' . ucfirst($period) . ')';
                    $chartData['bets_by_sport']['type'] = 'bar';
                }
            }

            // Revenue Trends Chart
            if ($chartType === 'all' || $chartType === 'revenue_trends') {
                $dateCondition = $this->getDateCondition($period);
                $revenueData = $this->rawSelect('dbTrxnRead',
                    "SELECT DATE(created_at) as date,
                            SUM(CASE WHEN reference_type_id = 2 THEN amount ELSE 0 END) as deposits,
                            SUM(CASE WHEN reference_type_id = 1 THEN amount ELSE 0 END) as withdrawals,
                            SUM(CASE WHEN reference_type_id = 3 THEN amount ELSE 0 END) as stakes
                     FROM transaction_summary
                     WHERE 1=1 $dateCondition
                     GROUP BY DATE(created_at)
                     ORDER BY date DESC
                     LIMIT 30", []);

                if ($revenueData) {
                    $chartData['revenue_trends'] = [
                        'title' => 'Revenue Trends (' . ucfirst($period) . ')',
                        'type' => 'line',
                        'data' => $revenueData
                    ];
                }
            }

            // Partner Performance Chart
            if ($chartType === 'all' || $chartType === 'partner_performance') {
                $dateCondition = $this->getDateCondition($period);
                $partnerData = $this->rawSelect('dbBetsRead',
                    "SELECT p.name as partner_name,
                            COUNT(pb.bet_id) as total_bets,
                            SUM(pb.bet_amount) as total_stakes,
                            SUM(CASE WHEN pb.status = 1 THEN pb.possible_win ELSE 0 END) as total_winnings,
                            (SUM(pb.bet_amount) - SUM(CASE WHEN pb.status = 1 THEN pb.possible_win ELSE 0 END)) as net_revenue
                     FROM partners_bets pb
                     JOIN partners p ON pb.partner_id = p.id
                     WHERE pb.status IN (0,1,2,3) $dateCondition
                     GROUP BY p.id, p.name
                     ORDER BY net_revenue DESC
                     LIMIT 10", []);

                if ($partnerData) {
                    $chartData['partner_performance'] = [
                        'title' => 'Top 10 Partners by Net Revenue (' . ucfirst($period) . ')',
                        'type' => 'bar',
                        'data' => $partnerData
                    ];
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Dashboard charts data retrieved successfully!',
                    'data' => $chartData,
                    'summary' => [
                        'total_charts' => count($chartData),
                        'period' => $period,
                        'chart_type' => $chartType
                    ]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * Helper method to get date condition based on period
     * @param string $period
     * @return string
     */
    private function getDateCondition($period)
    {
        $days = $this->getDaysFromPeriod($period);
        return " AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
    }

    /**
     * Helper method to get number of days from period string
     * @param string $period
     * @return int
     */
    private function getDaysFromPeriod($period)
    {
        switch ($period) {
            case '1day':
                return 1;
            case '7days':
                return 7;
            case '30days':
                return 30;
            case '90days':
                return 90;
            default:
                return 7;
        }
    }

}
