<?php

/**
 * Description of PartnerController
 *
 * <AUTHOR>
 */
class PartnerController extends \ControllerBase
{

    /**
     * GetPartners
     * @return type
     */
    public function GetPartners()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Partners";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartners :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $timestamp = $data['timestamp'] ?? false;
        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $export = $data['export'] ?? false;
        $status = $data['status'] ?? false;
        $start = $data['start'] ?? false;
        $stop = $data['end'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$timestamp || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) {
            $page = 1;
        }

        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }

        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'p.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND p.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND p.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND p.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $orderBy = $sort ? "ORDER BY $sort $order" : "";
                $exportLimit = 50000;
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            // select id,name,status,created_at,address,country,msisdn from partners;
            $sql = "SELECT (SELECT COUNT(p.id) FROM partners p  $searchQuery) as trx_count,"
                . "p.id, p.name, p.status, p.created_at, p.address, p.country, p.msisdn "
                . "FROM partners p $searchQuery $sorting";

            $results = $this->rawSelect('dbSportsRead', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Partners!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Partners successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreatePartner
     * @return type
     */
    public function CreatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartner :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $name = $data->name ?? false;
        $status = $data->status ?? 'active';
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$name) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate status
        if (!in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Check if partner name already exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE name = :name", [':name' => $name]);

            if ($existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner name already exists!'], true);
            }

            // Insert new partner
            $partnerId = $this->rawInsertBulk('dbUser', 'partners', [
                'name' => $name,
                'status' => $status,
                'address' => $address,
                'country' => $country,
                'msisdn' => $msisdn,
                'created_at' => $this->now()
            ]);

            if (!$partnerId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner created successfully!',
                    'data' => ['partner_id' => $partnerId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartner
     * @return type
     */
    public function UpdatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartner :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;
        $name = $data->name ?? false;
        $status = $data->status ?? false;
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate status if provided
        if ($status && !in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check if new name conflicts with existing partner (if name is being updated)
            if ($name && $name !== $existingPartner['name']) {
                $nameConflict = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM partners WHERE name = :name AND id != :id",
                    [':name' => $name, ':id' => $partnerId]);

                if ($nameConflict) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful',
                        ['code' => 409, 'message' => 'Partner name already exists!'], true);
                }
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerId];

            if ($name) {
                $updateFields[] = "name = :name";
                $updateParams[':name'] = $name;
            }
            if ($status) {
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }
            if ($address !== null) {
                $updateFields[] = "address = :address";
                $updateParams[':address'] = $address;
            }
            if ($country !== null) {
                $updateFields[] = "country = :country";
                $updateParams[':country'] = $country;
            }
            if ($msisdn !== null) {
                $updateFields[] = "msisdn = :msisdn";
                $updateParams[':msisdn'] = $msisdn;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partners SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * DeletePartner
     * @return type
     */
    public function DeletePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Delete Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request DeletePartner :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check for dependencies (partner services, settings, etc.)
            $dependencies = $this->rawSelectOneRecord('dbUser',
                "SELECT COUNT(*) as count FROM partner_services WHERE partner_id = :id",
                [':id' => $partnerId]);

            if ($dependencies && $dependencies['count'] > 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Cannot delete partner with existing services!'], true);
            }

            // Soft delete by setting status to inactive
            $result = $this->rawUpdateWithParams('dbUser',
                "UPDATE partners SET status = 'inactive' WHERE id = :id",
                [':id' => $partnerId]);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to delete partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner deleted successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerServices
     * @return type
     */
    public function GetPartnerServices()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Services";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerServices :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $params['limit'] = $data['limit'] ?? false;
        $params['skipCache'] = $data['skip_cache'] ?? false;
        $params['sort'] = $data['sort'] ?? false;
        $page = $data['page'] ?? false;
        $partnerId = $data['partner_id'] ?? false;
        $serviceId = $data['service_id'] ?? false;
        $status = $data['status'] ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        if (!$page) $page = 1;
        if (!$params['limit'] || !is_numeric($params['limit'])) {
            $params['limit'] = $this->settings['SelectRecordLimit'];
        }
        if (!in_array($params['skipCache'], [1, 2])) {
            $params['skipCache'] = 1;
        }

        $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
        if (count($order_arr) > 1) {
            $sort = "ps." . $this->cleanStrSQL($order_arr[0]);
            $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';
            if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                $order = 'DESC';
            }
        } else {
            $sort = 'ps.id';
            $order = 'DESC';
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            if (is_numeric($partnerId)) {
                $searchParams[':partner_id'] = $partnerId;
                $searchQuery .= " AND ps.partner_id = :partner_id";
            }

            if (is_numeric($serviceId)) {
                $searchParams[':service_id'] = $serviceId;
                $searchQuery .= " AND ps.service_id = :service_id";
            }

            if ($status) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND ps.status = :status";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);

            $sql = "SELECT (SELECT COUNT(ps.id) FROM partner_services ps $searchQuery) as trx_count,"
                . "ps.id, ps.partner_id, ps.service_id, ps.rate_limit_per_minute, ps.status, ps.created_at, "
                . "p.name as partner_name, s.name as service_name "
                . "FROM partner_services ps "
                . "LEFT JOIN partners p ON ps.partner_id = p.id "
                . "LEFT JOIN services s ON ps.service_id = s.id "
                . "$searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Request returned no Partner Services!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Services successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartnerService
     * @return type
     */
    public function CreatePartnerService()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Services";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerService :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;
        $serviceId = $data->service_id ?? false;
        $rateLimitPerMinute = $data->rate_limit_per_minute ?? 60;
        $status = $data->status ?? 'active';

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId || !$serviceId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate rate limit
        if (!is_numeric($rateLimitPerMinute) || $rateLimitPerMinute < 1 || $rateLimitPerMinute > 1000) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 1000"], true);
        }

        // Validate status
        if (!in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Validate partner exists
            $partner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id AND status = 'active'", [':id' => $partnerId]);

            if (!$partner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found or inactive!'], true);
            }

            // Validate service exists
            $service = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM services WHERE id = :id", [':id' => $serviceId]);

            if (!$service) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Service not found!'], true);
            }

            // Check if partner service already exists
            $existingService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_services WHERE partner_id = :partner_id AND service_id = :service_id",
                [':partner_id' => $partnerId, ':service_id' => $serviceId]);

            if ($existingService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner service already exists!'], true);
            }

            // Insert new partner service
            $partnerServiceId = $this->rawInsertBulk('dbUser', 'partner_services', [
                'partner_id' => $partnerId,
                'service_id' => $serviceId,
                'rate_limit_per_minute' => $rateLimitPerMinute,
                'status' => $status,
                'created_at' => $this->now()
            ]);

            if (!$partnerServiceId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner service!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner service created successfully!',
                    'data' => ['partner_service_id' => $partnerServiceId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerService
     * @return type
     */
    public function UpdatePartnerService($partnerServiceId) {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Services";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerService :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $rateLimitPerMinute = $data->rate_limit_per_minute ?? false;
        $status = $data->status ?? false;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate rate limit if provided
        if ($rateLimitPerMinute !== false && (!is_numeric($rateLimitPerMinute) || $rateLimitPerMinute < 1 || $rateLimitPerMinute > 1000)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 1000"], true);
        }

        // Validate status if provided
        if ($status !== false && !in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Validate partner service exists
            $partnerService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_services WHERE id = :id", [':id' => $partnerServiceId]);

            if (!$partnerService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner service not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerServiceId];

            if ($rateLimitPerMinute !== null) {
                $updateFields[] = "rate_limit_per_minute = :rate_limit_per_minute";
                $updateParams[':rate_limit_per_minute'] = $rateLimitPerMinute;
            }
            if ($status !== null) {
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partner_services SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner service!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner service updated successfully!'], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnersBets
     * @return type
     */
    public function GetPartnersBets()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bets";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnersBets :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_currency', 'bet_type', 'status', 'start_date', 'end_date'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pb.bet_id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            if ($params['partner_id']) {
                $searchParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pb.partner_id = :partner_id";
            }

            if ($params['bet_currency']) {
                $searchParams[':bet_currency'] = $params['bet_currency'];
                $searchQuery .= " AND pb.bet_currency = :bet_currency";
            }

            if ($params['bet_type']) {
                $searchParams[':bet_type'] = $params['bet_type'];
                $searchQuery .= " AND pb.bet_type = :bet_type";
            }

            if ($params['status']) {
                $searchParams[':status'] = $params['status'];
                $searchQuery .= " AND pb.status = :status";
            }

            if ($params['start_date']) {
                $searchParams[':start_date'] = $params['start_date'];
                $searchQuery .= " AND DATE(pb.created_at) >= :start_date";
            }

            if ($params['end_date']) {
                $searchParams[':end_date'] = $params['end_date'];
                $searchQuery .= " AND DATE(pb.created_at) <= :end_date";
            }

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pb.bet_id) FROM partners_bets pb $searchQuery) as trx_count,
                      pb.bet_id, pb.partner_id, p.name as partner_name, pb.profile_id, pb.bet_currency,
                      pb.bet_amount, pb.bet_reference, pb.bet_transaction_id, pb.bet_credit_transaction_id,
                      pb.bet_type, pb.total_games, pb.live_events, pb.total_odd, pb.possible_win,
                      pb.witholding_tax, pb.excise_tax, pb.bet_attribution, pb.browser_details,
                      pb.extra_data, pb.created_by, pb.kra_report, pb.risk_state, pb.processed,
                      pb.status, pb.created_at, pb.updated_at
                      FROM partners_bets pb
                      LEFT JOIN partners p ON pb.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bets found.'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnersBetSlips
     * @return type
     */
    public function GetPartnersBetSlips()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bet Slips";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnersBetSlips :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_id', 'sport_id', 'status', 'live_bet'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pbs.slip_id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            if ($params['partner_id']) {
                $searchParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pbs.partner_id = :partner_id";
            }

            if ($params['bet_id']) {
                $searchParams[':bet_id'] = $params['bet_id'];
                $searchQuery .= " AND pbs.bet_id = :bet_id";
            }

            if ($params['sport_id']) {
                $searchParams[':sport_id'] = $params['sport_id'];
                $searchQuery .= " AND pbs.sport_id = :sport_id";
            }

            if ($params['status']) {
                $searchParams[':status'] = $params['status'];
                $searchQuery .= " AND pbs.status = :status";
            }

            if ($params['live_bet']) {
                $searchParams[':live_bet'] = $params['live_bet'];
                $searchQuery .= " AND pbs.live_bet = :live_bet";
            }

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pbs.slip_id) FROM partners_bet_slips pbs $searchQuery) as trx_count,
                      pbs.slip_id, pbs.partner_id, p.name as partner_name, pbs.bet_id, pbs.sport_id,
                      pbs.parent_match_id, pbs.parent_market_id, pbs.market_id, pbs.selection_id,
                      pbs.outcome_name, pbs.odd_value, pbs.pick, pbs.pick_name, pbs.winning_outcome,
                      pbs.ht_scores, pbs.ft_scores, pbs.et_scores, pbs.extra_data, pbs.live_bet,
                      pbs.status, pbs.resulting_type, pbs.start_time, pbs.created_at, pbs.updated_at
                      FROM partners_bet_slips pbs
                      LEFT JOIN partners p ON pbs.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bet slips found.'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner bet slips successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerBalance
     * @return type
     */
    public function GetPartnerBalance()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Balance";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerBalance :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'status'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pb.id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $queryParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pb.partner_id = :partner_id";
            }

            if ($params['status']) {
                $queryParams[':status'] = $params['status'];
                $searchQuery .= " AND pb.status = :status";
            }

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pb.id) FROM partner_balance pb $searchQuery) as trx_count,
                      pb.id, pb.partner_id, p.name as partner_name, pb.balance, pb.bonus,
                      pb.status, pb.updated_at
                      FROM partner_balance pb
                      LEFT JOIN partners p ON pb.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner balance information is currently available.'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner balance records successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerBalanceTransactions
     * @return type
     */
    public function GetPartnerBalanceTransactions()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Balance Transactions";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerBalanceTransactions :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'transaction_type', 'status', 'start_date', 'end_date'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pbt.id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $queryParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pbt.partner_id = :partner_id";
            }

            if ($params['transaction_type']) {
                $queryParams[':transaction_type'] = $params['transaction_type'];
                $searchQuery .= " AND pbt.transaction_type = :transaction_type";
            }

            if ($params['status']) {
                $queryParams[':status'] = $params['status'];
                $searchQuery .= " AND pbt.status = :status";
            }

            if ($params['start_date']) {
                $queryParams[':start_date'] = $params['start_date'];
                $searchQuery .= " AND DATE(pbt.created_at) >= :start_date";
            }

            if ($params['end_date']) {
                $queryParams[':end_date'] = $params['end_date'];
                $searchQuery .= " AND DATE(pbt.created_at) <= :end_date";
            }

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query - using partner_balance_transactions table
            $query = "SELECT (SELECT COUNT(pbt.id) FROM partner_balance_transactions pbt $searchQuery) as trx_count,
                      pbt.id, pbt.partner_id, p.name as partner_name, pbt.transaction_type,
                      pbt.amount, pbt.balance_before, pbt.balance_after, pbt.reference_id,
                      pbt.description, pbt.status, pbt.created_at, pbt.updated_at,
                      pb.balance as current_balance, pb.bonus as current_bonus
                      FROM partner_balance_transactions pbt
                      LEFT JOIN partners p ON pbt.partner_id = p.id
                      LEFT JOIN partner_balance pb ON pbt.partner_id = pb.partner_id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner balance transactions found.'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner balance transactions successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerBalanceSummary
     * @return type
     */
    public function GetPartnerBalanceSummary()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Balance Summary";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerBalanceSummary :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build search query for specific partner or all partners
            $searchQuery = "WHERE pb.status = 1";
            $queryParams = [];

            if ($params['partner_id']) {
                $queryParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pb.partner_id = :partner_id";
            }

            // Get summary statistics
            $summaryQuery = "SELECT
                COUNT(pb.id) as total_partners,
                SUM(pb.balance) as total_balance,
                SUM(pb.bonus) as total_bonus,
                AVG(pb.balance) as average_balance,
                AVG(pb.bonus) as average_bonus,
                MAX(pb.balance) as max_balance,
                MIN(pb.balance) as min_balance,
                COUNT(CASE WHEN pb.balance > 0 THEN 1 END) as partners_with_balance,
                COUNT(CASE WHEN pb.bonus > 0 THEN 1 END) as partners_with_bonus
                FROM partner_balance pb
                LEFT JOIN partners p ON pb.partner_id = p.id
                $searchQuery";

            $summaryResult = $this->rawSelectOneRecord('dbUser', $summaryQuery, $queryParams);

            if (!$summaryResult) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner balance data found.'], true);
            }

            // Get top 10 partners by balance
            $topPartnersQuery = "SELECT
                pb.partner_id, p.name as partner_name, pb.balance, pb.bonus,
                pb.updated_at
                FROM partner_balance pb
                LEFT JOIN partners p ON pb.partner_id = p.id
                WHERE pb.status = 1
                ORDER BY pb.balance DESC
                LIMIT 10";

            $topPartners = $this->rawSelect('dbUser', $topPartnersQuery, []);

            // Format the response data
            $responseData = [
                'summary' => [
                    'total_partners' => (int)$summaryResult['total_partners'],
                    'total_balance' => number_format((float)$summaryResult['total_balance'], 2),
                    'total_bonus' => number_format((float)$summaryResult['total_bonus'], 2),
                    'average_balance' => number_format((float)$summaryResult['average_balance'], 2),
                    'average_bonus' => number_format((float)$summaryResult['average_bonus'], 2),
                    'max_balance' => number_format((float)$summaryResult['max_balance'], 2),
                    'min_balance' => number_format((float)$summaryResult['min_balance'], 2),
                    'partners_with_balance' => (int)$summaryResult['partners_with_balance'],
                    'partners_with_bonus' => (int)$summaryResult['partners_with_bonus']
                ],
                'top_partners' => $topPartners ?: []
            ];

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner balance summary retrieved successfully!',
                    'data' => $responseData], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnerSettings
     * @return type
     */
    public function GetPartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Settings";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerSettings :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'api_key'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'ps.id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $queryParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND ps.partner_id = :partner_id";
            }

            if ($params['api_key']) {
                $queryParams[':api_key'] = $params['api_key'];
                $searchQuery .= " AND ps.api_key = :api_key";
            }

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(ps.id) FROM partner_settings ps $searchQuery) as trx_count,
                      ps.id, ps.partner_id, p.name as partner_name, ps.api_key, ps.ip_address,
                      ps.callback_url, ps.currency, ps.denomination, ps.timezone, ps.billing_mode,
                      ps.rate_limit, ps.websites, ps.version, ps.created_at
                      FROM partner_settings ps
                      LEFT JOIN partners p ON ps.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner settings found.'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner settings successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartnerSettings
     * @return type
     */
    public function CreatePartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerSettings :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;
        $apiKey = $data->api_key ?? false;
        $ipAddress = $data->ip_address ?? null;
        $callbackUrl = $data->callback_url ?? null;
        $currency = $data->currency ?? 'USD';
        $denomination = $data->denomination ?? 'cents';
        $timezone = $data->timezone ?? 'UTC';
        $billingMode = $data->billing_mode ?? 'prepay';
        $rateLimit = $data->rate_limit ?? 60;
        $websites = $data->websites ?? null;
        $version = $data->version ?? 1;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId || !$apiKey) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate billing mode
        if (!in_array($billingMode, ['prepay', 'postpay'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid billing mode. Must be 'prepay' or 'postpay'"], true);
        }

        // Validate rate limit
        if (!is_numeric($rateLimit) || $rateLimit < 1 || $rateLimit > 10000) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 10000"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Validate partner exists
            $partner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id AND status = 'active'", [':id' => $partnerId]);

            if (!$partner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found or inactive!'], true);
            }

            // Check if partner settings already exist
            $existingSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE partner_id = :partner_id",
                [':partner_id' => $partnerId]);

            if ($existingSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner settings already exist!'], true);
            }

            // Check if API key is unique
            $existingApiKey = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE api_key = :api_key",
                [':api_key' => $apiKey]);

            if ($existingApiKey) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'API key already exists!'], true);
            }

            // Insert new partner settings
            $partnerSettingsId = $this->rawInsertBulk('dbUser', 'partner_settings', [
                'partner_id' => $partnerId,
                'api_key' => $apiKey,
                'ip_address' => $ipAddress,
                'callback_url' => $callbackUrl,
                'currency' => $currency,
                'denomination' => $denomination,
                'timezone' => $timezone,
                'billing_mode' => $billingMode,
                'rate_limit' => $rateLimit,
                'websites' => $websites ? json_encode($websites) : null,
                'version' => $version,
                'created_at' => $this->now()
            ]);

            if (!$partnerSettingsId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner settings created successfully!',
                    'data' => ['partner_settings_id' => $partnerSettingsId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerSettings
     * @return type
     */
    public function UpdatePartnerSettings($partnerSettingsId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerSettings :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $ipAddress = $data->ip_address ?? null;
        $callbackUrl = $data->callback_url ?? null;
        $currency = $data->currency ?? null;
        $denomination = $data->denomination ?? null;
        $timezone = $data->timezone ?? null;
        $billingMode = $data->billing_mode ?? null;
        $rateLimit = $data->rate_limit ?? null;
        $websites = $data->websites ?? null;
        $version = $data->version ?? null;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate billing mode if provided
        if ($billingMode !== null && !in_array($billingMode, ['prepay', 'postpay'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid billing mode. Must be 'prepay' or 'postpay'"], true);
        }

        // Validate rate limit if provided
        if ($rateLimit !== null && (!is_numeric($rateLimit) || $rateLimit < 1 || $rateLimit > 10000)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 10000"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Security Error. Wrong authenticated request!'], true);
            }

            $authResponse = UserUtils::QuickAuthenticate($accessToken);
            if (!$authResponse || !in_array($authResponse['code'], ['200'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request Requires Authentication',
                    ['code' => $authResponse['code'], 'message' => $authResponse['message']], true);
            }

            if (!UserUtils::ValidateUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 403, 'message' => "Action require privileged access!"], true);
            }

            // Validate partner settings exist
            $partnerSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE id = :id", [':id' => $partnerSettingsId]);

            if (!$partnerSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner settings not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerSettingsId];

            if ($ipAddress !== null) {
                $updateFields[] = "ip_address = :ip_address";
                $updateParams[':ip_address'] = $ipAddress;
            }
            if ($callbackUrl !== null) {
                $updateFields[] = "callback_url = :callback_url";
                $updateParams[':callback_url'] = $callbackUrl;
            }
            if ($currency !== null) {
                $updateFields[] = "currency = :currency";
                $updateParams[':currency'] = $currency;
            }
            if ($denomination !== null) {
                $updateFields[] = "denomination = :denomination";
                $updateParams[':denomination'] = $denomination;
            }
            if ($timezone !== null) {
                $updateFields[] = "timezone = :timezone";
                $updateParams[':timezone'] = $timezone;
            }
            if ($billingMode !== null) {
                $updateFields[] = "billing_mode = :billing_mode";
                $updateParams[':billing_mode'] = $billingMode;
            }
            if ($rateLimit !== null) {
                $updateFields[] = "rate_limit = :rate_limit";
                $updateParams[':rate_limit'] = $rateLimit;
            }
            if ($websites !== null) {
                $updateFields[] = "websites = :websites";
                $updateParams[':websites'] = json_encode($websites);
            }
            if ($version !== null) {
                $updateFields[] = "version = :version";
                $updateParams[':version'] = $version;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partner_settings SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner settings updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

}