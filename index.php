<?php

global $di;

use Phalcon\Mvc\Micro;
use Phalcon\Loader;
use Phalcon\Di\FactoryDefault;
use Phalcon\Db\Adapter\Pdo\Mysql as PdoMysql;
use Phalcon\Http\Response;
use Phalcon\Mvc\Micro\Collection as MicroCollection;
use Phalcon\Mvc\Micro\Exception;
use Phalcon\Events\Manager as Manager;
use FirewallMiddleware as firewall;
use NotFoundMiddleware as notFound;

define('S_HOST', gethostname());
$env = 'PROD';
$phalcon = 4;
$err = 0;

if (in_array(S_HOST, ['ke-pr-web-1'])) {
    $env = 'DEV';
    $phalcon = 3;
    $err = 1;
    error_reporting(E_ALL);
}

define('APP_PATH', realpath(''));
define('PHALCON_VERSION', $phalcon);
define('ENVIRONMENT', $env);

ini_set('display_errors', $err);
ini_set('display_startup_errors', $err);
ini_set("date.timezone", "Africa/Nairobi");
ini_set('default_socket_timeout', 160);

/**
 * Read auto-loader
 */
include APP_PATH . "/vendor/autoload.php";

/**
 * Read the configuration
 */
$config = include APP_PATH . "/app/config/config.php";

/**
 * Read auto-loader
 */
include APP_PATH . "/app/config/loader.php";

/**
 * Read services
 */
include APP_PATH . "/app/config/services.php";

/**
 * create and bind the DI to the application
 */
$di = new FactoryDefault();
$app = new Micro($di);

/**
 * Create a new Events Manager.
 */
$manager = new Manager();





/**
 * User
 */
$system = new MicroCollection();
$system->setPrefix('/system/');
$system->setHandler(UserController::class, true);

// User authentication and account management
$system->mapVia('v1/user/login', 'UserAccountLogin', ['POST']);
$system->mapVia('v1/user/login_verify', 'VerifyLoginOTP', ['POST']);
$system->mapVia('v1/user/resend_otp/{user_id}', 'ResendUserOTP', ['POST', 'GET']);
$system->mapVia('v1/user/validate_otp', 'ValidateAuthOTP', ['POST']);

$system->mapVia('v1/user/account_create', 'CreateUserAccount', ['POST']);
$system->mapVia('v1/user/account_edit/{user_id}', 'EditUserAccount', ['POST', 'GET']);
$system->mapVia('v1/user/password_reset', 'ResetUserAccountPassword', ['POST']);
$system->mapVia('v1/user/password_change', 'ChangeUserAccountPassword', ['POST']);

// Roles management
$system->mapVia('v1/roles', 'GetUserRoles', ['GET']);
$system->mapVia('v1/role/create', 'CreateUserRoles', ['POST']);
$system->mapVia('v1/role/update/{roleId}', 'EditUserRoles', ['POST']);

// Permissions management
$system->mapVia('v1/permissions', 'GetUserPermissions', ['GET']);
$system->mapVia('v1/permission/create', 'CreatePermission', ['POST']);
$system->mapVia('v1/permission/update/{permissionId}', 'EditPermission', ['POST']);

// System users management
$system->mapVia('v1/users', 'GetSystemUsers', ['POST', 'GET']);
$system->mapVia('v1/users/edit/{user_id}', 'EditUserAccount', ['POST']);
$system->mapVia('v1/users/blacklist/{user_id}', 'BlacklistUser', ['POST']);



/**
 * Dashboard
 */
$dashboard = new MicroCollection();
$dashboard->setPrefix('/dashboard/');
$dashboard->setHandler(DashboardController::class, true);
$dashboard->mapVia('v1/view', 'ViewDashboardStats', ['POST', 'GET']);
$dashboard->mapVia('v1/charts', 'GetDashboardCharts', ['POST', 'GET']);



/**
 * Partners
 */
$partner = new MicroCollection();
$partner->setPrefix('/partners/');
$partner->setHandler(PartnerController::class, true);

// Partner management
$partner->mapVia('v1/view', 'GetPartners', ['POST', 'GET']);
$partner->mapVia('v1/create', 'CreatePartner', ['POST']);
$partner->mapVia('v1/update/{partnerId}', 'UpdatePartner', ['POST']);
$partner->mapVia('v1/delete/{partnerId}', 'DeletePartner', ['POST']);

// Partner services
$partner->mapVia('v1/partner_services', 'GetPartnerServices', ['POST', 'GET']);
$partner->mapVia('v1/service/create', 'CreatePartnerService', ['POST']);
$partner->mapVia('v1/service/update/{serviceId}', 'UpdatePartnerService', ['POST']);

// Partner data and reports
$partner->mapVia('v1/partners_bets', 'GetPartnersBets', ['POST', 'GET']);
$partner->mapVia('v1/partners_bet_slips', 'GetPartnersBetSlips', ['POST', 'GET']);
$partner->mapVia('v1/partner_balance', 'GetPartnerBalance', ['POST', 'GET']);
$partner->mapVia('v1/partner_balance_transactions', 'GetPartnerBalanceTransactions', ['POST', 'GET']);
$partner->mapVia('v1/partner_balance_summary', 'GetPartnerBalanceSummary', ['POST', 'GET']);

// Partner settings
$partner->mapVia('v1/partner_settings', 'GetPartnerSettings', ['POST', 'GET']);
$partner->mapVia('v1/partner_settings/create', 'CreatePartnerSettings', ['POST']);
$partner->mapVia('v1/partner_settings/update/{settingsId}', 'UpdatePartnerSettings', ['POST']);



/**
 * Bets
 */
$bets = new MicroCollection();
$bets->setPrefix('/bets/');
$bets->setHandler(BetsController::class, true);
# Sports-Bets
$bets->mapVia('v1/partner_bets', 'GetPartnerBets', ['POST', 'GET']);
$bets->mapVia('v1/partners_bet_slips/{partnerId}', 'GetPartnersBetSlips', ['POST', 'GET']);



/**
 * Reports
 */
$reports = new MicroCollection();
$reports->setPrefix('/reports/');
$reports->setHandler(ReportController::class, true);
$reports->mapVia('v1/outbox', 'GetSmsOutbox', ['POST', 'GET']);
$reports->mapVia('v1/sms_filters', 'BlastSMSFilters', ['GET', 'POST']);


/**
 * Transactions
 */
$trxns = new MicroCollection();
$trxns->setPrefix('/trxns/');
$trxns->setHandler(TransactionsController::class, true);
$trxns->mapVia('v1/all', 'GetTransactions', ['POST', 'GET']);


#Mount Points
$app->mount($system);
$app->mount($dashboard);
$app->mount($bets);
$app->mount($partner);

$app->mount($reports);
$app->mount($trxns);

try {
    if ($app->request->getMethod() == "OPTIONS") {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header("Access-Control-Allow-Headers: X-App-Key,X-Hash-Key,X-Access,X-Authorization"
                . ",X-Authorization-Key,X-Source,X-Campaign,X-Authorization-Key,Authorization,X-Requested-With,Content-Disposition,Origin,accept,X-Access,X-Signature,Content-Type,Access-Control-Request-Method,Access-Control-Request-Headers,Access-Control-Allow-Origin");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Expose-Headers: Content-Length,X-JSON');
        header("HTTP/1.1 200 OK");
        exit();
    }

    $app->after(function () use ($app) {
        $app->response
                ->setHeader("Accept", "*/*")
                ->setHeader("Accept-Encoding", "gzip")
                ->setHeader("Accept-Charset", "utf-8")
                ->setHeader("Access-Control-Allow-Credentials", true)
                ->setHeader("Access-Control-Allow-Methods", 'GET,POST')
                ->setHeader("Access-Control-Allow-Origin", '*')
                ->setHeader('Access-Control-Allow-Headers', 'X-App-Key,X-Hash-Key,X-Access,X-Authorization,X-Source,X-Campaign,X-Authorization-Key,Authorization,X-Requested-With,Content-Disposition,Origin,accept,X-Access,X-Signature,Content-Type,Access-Control-Request-Method,Access-Control-Request-Headers,Access-Control-Allow-Origin')
                ->setHeader('Access-Control-Expose-Headers', 'Content-Length,X-JSON');
        $app->response->sendHeaders();
        return true;
    });

    /**
     * Not Found URLs
     */
    $app->notFound(function () use ($app) {
        $nfm = new notFound();
        $nfm->before($app);
    });

    $app->setEventsManager($manager);

    // Handle the request
    header('Access-Control-Allow-Origin:*');

    if (PHALCON_VERSION == 4) {
        $response = $app->handle($_SERVER['REQUEST_URI']);
    } else {
        $response = $app->handle();
    }
} catch (Exception $e) {
    $res = new \stdClass();
    $res->code = 500;
    $res->statusDescription = "Request is not successful. ";
    $res->data = ['code' => 500, 'message' => "[SEVERE ERROR] Internal server error!"];

    $app->response->setStatusCode(500, 'Internal Server Error')
            ->setJsonContent($res)
            ->send();
}

// ATBBXfQWyY4rPJFTwQyfQR59S6YhEA45CE6D


